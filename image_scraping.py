import sys, time, re, random, string, os, datetime, shutil, sqlite3, tempfile
from pathlib import Path
from threading import Thread
from DrissionPage import ChromiumPage, ChromiumOptions
from playwright_stealth import Stealth
from PyQt6.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
    QPushButton, QMessageBox, QGroupBox, QComboBox, QTableWidget, QTableWidgetItem,
    QProgressBar, QDialog, QToolButton, QMenu, QHeaderView, QTextEdit, QFrame
)
from PyQt6.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, QEvent
from PyQt6.QtGui import QPalette

# Import module Google Sheet manager
from gsheet_manager import GoogleSheetManager

# Import CloudflareBypasser
try:
    from CloudflareBypasser import CloudflareBypasser
    CLOUDFLARE_BYPASSER_AVAILABLE = True
except ImportError:
    print("CloudflareBypasser không khả dụng")
    CLOUDFLARE_BYPASSER_AVAILABLE = False

class ScrapingDataManager:
    """SQLite manager để lưu trữ tạm thời data scraping, tránh memory leak"""

    def __init__(self):
        # Tạo database trong AppData\Local\Data All in One\Image Scrape
        local_appdata = os.environ.get('LOCALAPPDATA', os.path.join(os.path.expanduser('~'), 'AppData', 'Local'))
        data_dir = os.path.join(local_appdata, 'Data All in One', 'Image Scrape')
        os.makedirs(data_dir, exist_ok=True)

        self.db_path = os.path.join(data_dir, 'scraping_temp.db')
        self.conn = sqlite3.connect(self.db_path, check_same_thread=False)
        self.setup_tables()

    def setup_tables(self):
        """Tạo bảng lưu trữ kết quả scraping"""
        cursor = self.conn.cursor()
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS scraping_results (
                row_num INTEGER PRIMARY KEY,
                product_link TEXT NOT NULL,
                image_url TEXT,
                status TEXT,
                error_message TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        self.conn.commit()

    def add_result(self, row_num, product_link, image_url=None, status="processing", error_message=None):
        """Thêm hoặc cập nhật kết quả scraping"""
        cursor = self.conn.cursor()
        cursor.execute('''
            INSERT OR REPLACE INTO scraping_results
            (row_num, product_link, image_url, status, error_message)
            VALUES (?, ?, ?, ?, ?)
        ''', (row_num, product_link, image_url, status, error_message))
        self.conn.commit()

    def get_all_results(self):
        """Lấy tất cả kết quả để upload - format như file logic (list of tuples)"""
        cursor = self.conn.cursor()
        cursor.execute('SELECT row_num, image_url FROM scraping_results WHERE image_url IS NOT NULL')
        return cursor.fetchall()  # Return list of tuples như file logic

    def get_results_count(self):
        """Đếm số kết quả đã có"""
        cursor = self.conn.cursor()
        cursor.execute('SELECT COUNT(*) FROM scraping_results WHERE image_url IS NOT NULL')
        return cursor.fetchone()[0]

    def clear_all(self):
        """Xóa tất cả data sau khi upload thành công"""
        cursor = self.conn.cursor()
        cursor.execute('DELETE FROM scraping_results')
        self.conn.commit()

    def close(self):
        """Đóng kết nối database"""
        if self.conn:
            self.conn.close()

# Function để detect Dark Mode của Windows
def is_windows_dark_mode():
    """Kiểm tra xem Windows có đang sử dụng Dark Mode không"""
    try:
        import winreg
        registry = winreg.ConnectRegistry(None, winreg.HKEY_CURRENT_USER)
        key = winreg.OpenKey(registry, r"Software\Microsoft\Windows\CurrentVersion\Themes\Personalize")
        value, _ = winreg.QueryValueEx(key, "AppsUseLightTheme")
        winreg.CloseKey(key)
        return value == 0  # 0 = Dark Mode, 1 = Light Mode
    except:
        return False  # Default to Light Mode if can't detect

# Function để tạo style cho Dark/Light Mode
def get_theme_styles(is_dark_mode=None):
    """Trả về styles phù hợp với Dark/Light Mode"""
    if is_dark_mode is None:
        is_dark_mode = is_windows_dark_mode()

    if is_dark_mode:
        return {
            'background': '#2b2b2b',
            'text': '#ffffff',
            'button_bg': '#404040',
            'button_hover': '#505050',
            'button_pressed': '#606060',
            'border': '#555555',
            'accent': '#0078d4',
            'success': '#107c10',
            'warning': '#ff8c00',
            'danger': '#d13438',
            'input_bg': '#383838',
            'table_bg': '#323232',
            'table_alternate': '#2a2a2a'
        }
    else:
        return {
            'background': '#ffffff',
            'text': '#000000',
            'button_bg': '#f0f0f0',
            'button_hover': '#e0e0e0',
            'button_pressed': '#d0d0d0',
            'border': '#cccccc',
            'accent': '#0078d4',
            'success': '#107c10',
            'warning': '#ff8c00',
            'danger': '#d13438',
            'input_bg': '#ffffff',
            'table_bg': '#ffffff',
            'table_alternate': '#f5f5f5'
        }

class DrissionPageManager:
    """Quản lý DrissionPage để thay thế Playwright."""

    def __init__(self, chrome_profile_dir, headless=False):
        self.chrome_profile_dir = chrome_profile_dir
        self.headless = headless
        self.page = None
        self.bypasser = None

    def create_page(self):
        """Tạo ChromiumPage với cấu hình tối ưu."""
        try:
            # Cấu hình ChromiumOptions
            options = ChromiumOptions()

            # Thiết lập user data dir
            options.set_user_data_path(self.chrome_profile_dir)

            # Cấu hình để tránh bị phát hiện
            options.set_argument('--disable-blink-features=AutomationControlled')
            options.set_argument('--disable-infobars')
            options.set_argument('--disable-features=IsolateOrigins,site-per-process')
            options.set_argument('--disable-site-isolation-trials')
            options.set_argument('--disable-web-security')
            options.set_argument('--disable-notifications')
            options.set_argument('--no-sandbox')
            # Loại bỏ --disable-setuid-sandbox vì không được hỗ trợ
            options.set_argument('--disable-dev-shm-usage')
            options.set_argument('--disable-gpu')
            options.set_argument('--disable-extensions')

            # User agent
            options.set_user_agent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')

            # Headless mode
            if self.headless:
                options.headless()

            # Tạo page
            self.page = ChromiumPage(addr_or_opts=options)

            # Khởi tạo CloudflareBypasser
            if CLOUDFLARE_BYPASSER_AVAILABLE:
                self.bypasser = CloudflareBypasser(self.page, max_retries=3, log=True)

            print(f"DrissionPage đã khởi tạo {'(headless)' if self.headless else '(có giao diện)'}")
            print(f"Profile: {self.chrome_profile_dir}")
            print(f"CloudflareBypasser: {'✅' if self.bypasser else '❌'}")

            return self.page

        except Exception as e:
            print(f"Lỗi khi tạo DrissionPage: {e}")
            return None

    def close(self):
        """Đóng page."""
        if self.page:
            try:
                self.page.quit()
            except:
                pass
            self.page = None

class PlaywrightCloudflareAdapter:
    """Adapter để sử dụng CloudflareBypasser với Playwright Page."""

    def __init__(self, playwright_page):
        self.page = playwright_page
        self.title = ""

    def update_title(self):
        """Cập nhật title từ Playwright page."""
        try:
            self.title = self.page.title()
        except:
            self.title = ""

    def eles(self, selector):
        """Tìm elements theo selector."""
        try:
            # Chuyển đổi selector từ DrissionPage sang Playwright
            if selector.startswith("tag:"):
                tag_name = selector.replace("tag:", "")
                elements = self.page.query_selector_all(tag_name)
            else:
                elements = self.page.query_selector_all(selector)

            return [PlaywrightElementAdapter(elem, self.page) for elem in elements]
        except:
            return []

    def ele(self, selector):
        """Tìm element đầu tiên theo selector."""
        try:
            if selector.startswith("tag:"):
                tag_name = selector.replace("tag:", "")
                element = self.page.query_selector(tag_name)
            else:
                element = self.page.query_selector(selector)

            if element:
                return PlaywrightElementAdapter(element, self.page)
        except:
            pass
        return None

class PlaywrightElementAdapter:
    """Adapter để sử dụng Playwright Element với CloudflareBypasser."""

    def __init__(self, playwright_element, page):
        self.element = playwright_element
        self.page = page
        self.attrs = {}
        self.shadow_root = None

        # Lấy attributes
        try:
            self.attrs = self.page.evaluate("""
                (element) => {
                    const attrs = {};
                    for (let attr of element.attributes) {
                        attrs[attr.name] = attr.value;
                    }
                    return attrs;
                }
            """, self.element)
        except:
            self.attrs = {}

    def parent(self):
        """Lấy parent element."""
        try:
            parent_elem = self.page.evaluate("element => element.parentElement", self.element)
            if parent_elem:
                return PlaywrightElementAdapter(parent_elem, self.page)
        except:
            pass
        return None

    def children(self):
        """Lấy children elements."""
        try:
            children = self.page.evaluate("element => Array.from(element.children)", self.element)
            return [PlaywrightElementAdapter(child, self.page) for child in children]
        except:
            return []

    def click(self):
        """Click element."""
        try:
            self.page.evaluate("element => element.click()", self.element)
        except Exception as e:
            print(f"Lỗi khi click element: {e}")

# ------------------------------
# Cấu hình OAuth BASE64 cho Google Sheet
# ------------------------------
OAUTH_CREDENTIALS_B64 = (
    "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
)

class ProfileManagerDialog(QDialog):
    def __init__(self, parent=None, chrome_profile_dir=""):
        super().__init__(parent)
        self.chrome_profile_dir = chrome_profile_dir
        self.parent_widget = parent
        self.setWindowTitle("Quản lý Chrome Profile")
        self.setFixedSize(600, 500)
        self.setWindowFlags(Qt.WindowType.Dialog | Qt.WindowType.WindowCloseButtonHint)

        self.init_ui()
        self.update_profile_info()

    def init_ui(self):
        # Lấy theme styles
        self.theme = get_theme_styles()

        layout = QVBoxLayout()

        # Title
        title_label = QLabel("🔧 Quản lý Chrome Profile")
        title_label.setStyleSheet(f"""
            font-size: 16px;
            font-weight: bold;
            margin: 10px;
            color: {self.theme['text']};
        """)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # Profile info section
        info_group = QGroupBox("📊 Thông tin Profile")
        info_group.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                color: {self.theme['text']};
                border: 2px solid {self.theme['border']};
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }}
        """)
        info_layout = QVBoxLayout()

        self.info_text = QTextEdit()
        self.info_text.setReadOnly(True)
        self.info_text.setMaximumHeight(150)
        self.info_text.setStyleSheet(f"""
            background-color: {self.theme['input_bg']};
            border: 1px solid {self.theme['border']};
            color: {self.theme['text']};
            border-radius: 3px;
            padding: 5px;
        """)
        info_layout.addWidget(self.info_text)

        info_group.setLayout(info_layout)
        layout.addWidget(info_group)

        # Management buttons
        buttons_group = QGroupBox("🛠️ Công cụ quản lý")
        buttons_group.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                color: {self.theme['text']};
                border: 2px solid {self.theme['border']};
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }}
        """)
        buttons_layout = QVBoxLayout()

        # Row 1: Refresh and Cleanup
        row1_layout = QHBoxLayout()

        self.refresh_btn = QPushButton("🔄 Làm mới thông tin")
        self.refresh_btn.clicked.connect(self.update_profile_info)
        self.refresh_btn.setFixedHeight(35)
        self.refresh_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {self.theme['button_bg']};
                color: {self.theme['text']};
                border: 1px solid {self.theme['border']};
                border-radius: 5px;
                padding: 5px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {self.theme['button_hover']};
            }}
            QPushButton:pressed {{
                background-color: {self.theme['button_pressed']};
            }}
        """)

        self.cleanup_btn = QPushButton("🧹 Dọn dẹp Profile")
        self.cleanup_btn.clicked.connect(self.cleanup_profile)
        self.cleanup_btn.setFixedHeight(35)
        self.cleanup_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {self.theme['warning']};
                color: white;
                border: 1px solid {self.theme['warning']};
                border-radius: 5px;
                padding: 5px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: #ffb84d;
            }}
            QPushButton:pressed {{
                background-color: #e67e00;
            }}
        """)

        row1_layout.addWidget(self.refresh_btn)
        row1_layout.addWidget(self.cleanup_btn)
        buttons_layout.addLayout(row1_layout)

        # Row 2: Reset and Open folder
        row2_layout = QHBoxLayout()

        self.reset_btn = QPushButton("🗑️ Xóa toàn bộ Profile")
        self.reset_btn.clicked.connect(self.reset_profile)
        self.reset_btn.setFixedHeight(35)
        self.reset_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {self.theme['danger']};
                color: white;
                border: 1px solid {self.theme['danger']};
                border-radius: 5px;
                padding: 5px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: #e74c3c;
            }}
            QPushButton:pressed {{
                background-color: #c0392b;
            }}
        """)

        self.open_folder_btn = QPushButton("📁 Mở thư mục Profile")
        self.open_folder_btn.clicked.connect(self.open_profile_folder)
        self.open_folder_btn.setFixedHeight(35)
        self.open_folder_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {self.theme['accent']};
                color: white;
                border: 1px solid {self.theme['accent']};
                border-radius: 5px;
                padding: 5px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: #106ebe;
            }}
            QPushButton:pressed {{
                background-color: #005a9e;
            }}
        """)

        row2_layout.addWidget(self.reset_btn)
        row2_layout.addWidget(self.open_folder_btn)
        buttons_layout.addLayout(row2_layout)

        buttons_group.setLayout(buttons_layout)
        layout.addWidget(buttons_group)

        # Status section
        status_group = QGroupBox("📋 Trạng thái")
        status_group.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                color: {self.theme['text']};
                border: 2px solid {self.theme['border']};
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }}
        """)
        status_layout = QVBoxLayout()

        self.status_label = QLabel("Sẵn sàng")
        self.status_label.setStyleSheet(f"""
            padding: 10px;
            background-color: {self.theme['success']};
            color: white;
            border: 1px solid {self.theme['success']};
            border-radius: 5px;
            font-weight: bold;
        """)
        status_layout.addWidget(self.status_label)

        status_group.setLayout(status_layout)
        layout.addWidget(status_group)

        # Close button
        close_btn = QPushButton("❌ Đóng")
        close_btn.clicked.connect(self.close)
        close_btn.setFixedHeight(40)
        close_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: #6c757d;
                color: white;
                border: 1px solid #6c757d;
                border-radius: 5px;
                padding: 5px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: #5a6268;
            }}
            QPushButton:pressed {{
                background-color: #545b62;
            }}
        """)
        layout.addWidget(close_btn)

        self.setLayout(layout)

        # Áp dụng theme cho toàn bộ dialog
        self.setStyleSheet(f"""
            QDialog {{
                background-color: {self.theme['background']};
                color: {self.theme['text']};
            }}
        """)

    def update_profile_info(self):
        """Cập nhật thông tin hiển thị về Chrome profile."""
        try:
            if os.path.exists(self.chrome_profile_dir):
                # Tính kích thước profile
                total_size = 0
                file_count = 0
                for dirpath, _, filenames in os.walk(self.chrome_profile_dir):
                    for filename in filenames:
                        filepath = os.path.join(dirpath, filename)
                        try:
                            total_size += os.path.getsize(filepath)
                            file_count += 1
                        except (OSError, IOError):
                            pass

                size_mb = total_size / (1024 * 1024)

                # Kiểm tra các thành phần quan trọng
                default_dir = os.path.join(self.chrome_profile_dir, 'Default')
                cookies_exist = os.path.exists(os.path.join(default_dir, 'Cookies'))
                session_exist = os.path.exists(os.path.join(default_dir, 'Sessions'))
                history_exist = os.path.exists(os.path.join(default_dir, 'History'))

                info_text = f"""📁 Đường dẫn: {self.chrome_profile_dir}

💾 Thông tin dung lượng:
   • Kích thước: {size_mb:.1f} MB
   • Số file: {file_count:,} files

🔐 Trạng thái lưu trữ:
   • Cookies: {'✅ Có' if cookies_exist else '❌ Chưa có'}
   • Session: {'✅ Có' if session_exist else '❌ Chưa có'}
   • History: {'✅ Có' if history_exist else '❌ Chưa có'}

ℹ️ Profile này sẽ lưu đăng nhập tự động và giúp tránh captcha."""

                self.status_label.setText("✅ Profile hoạt động bình thường")
                self.status_label.setStyleSheet("padding: 10px; background-color: #e8f5e8; border: 1px solid #4caf50; border-radius: 5px;")
            else:
                info_text = f"""📁 Đường dẫn: {self.chrome_profile_dir}

💾 Thông tin dung lượng:
   • Kích thước: Chưa tạo
   • Số file: 0 files

🔐 Trạng thái lưu trữ:
   • Cookies: ❌ Chưa có
   • Session: ❌ Chưa có
   • History: ❌ Chưa có

ℹ️ Profile sẽ được tạo khi chạy scraping lần đầu."""

                self.status_label.setText("⚠️ Profile chưa được tạo")
                self.status_label.setStyleSheet("padding: 10px; background-color: #fff3cd; border: 1px solid #ffc107; border-radius: 5px;")

            self.info_text.setText(info_text)

        except Exception as e:
            error_text = f"❌ Lỗi khi đọc thông tin profile:\n{str(e)}"
            self.info_text.setText(error_text)
            self.status_label.setText("❌ Có lỗi xảy ra")
            self.status_label.setStyleSheet("padding: 10px; background-color: #f8d7da; border: 1px solid #dc3545; border-radius: 5px;")

    def cleanup_profile(self):
        """Dọn dẹp profile thủ công."""
        if not os.path.exists(self.chrome_profile_dir):
            QMessageBox.information(self, "Thông báo", "Chrome Profile chưa tồn tại.")
            return

        reply = QMessageBox.question(
            self, "Xác nhận dọn dẹp",
            "Bạn có muốn dọn dẹp Chrome Profile?\n\n"
            "⚠️ Lưu ý: Cookies và session sẽ được giữ lại,\n"
            "chỉ xóa các file tạm và cache không cần thiết.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            try:
                if self.parent_widget:
                    self.parent_widget.cleanup_chrome_profile()
                self.update_profile_info()
                QMessageBox.information(self, "Hoàn thành", "Đã dọn dẹp Chrome Profile thành công!")
            except Exception as e:
                QMessageBox.critical(self, "Lỗi", f"Có lỗi khi dọn dẹp: {str(e)}")

    def reset_profile(self):
        """Xóa toàn bộ profile."""
        if not os.path.exists(self.chrome_profile_dir):
            QMessageBox.information(self, "Thông báo", "Chrome Profile chưa tồn tại.")
            return

        reply = QMessageBox.question(
            self, "⚠️ Xác nhận xóa",
            "Bạn có chắc chắn muốn XÓA TOÀN BỘ Chrome Profile?\n\n"
            "🚨 CẢNH BÁO: Hành động này sẽ:\n"
            "• Xóa tất cả cookies và session\n"
            "• Xóa lịch sử duyệt web\n"
            "• Buộc phải đăng nhập lại tất cả tài khoản\n\n"
            "Hành động này KHÔNG THỂ HOÀN TÁC!",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            try:
                shutil.rmtree(self.chrome_profile_dir)
                os.makedirs(self.chrome_profile_dir)
                self.update_profile_info()
                QMessageBox.information(self, "Hoàn thành", "Đã xóa toàn bộ Chrome Profile!")
            except Exception as e:
                QMessageBox.critical(self, "Lỗi", f"Có lỗi khi xóa profile: {str(e)}")

    def open_profile_folder(self):
        """Mở thư mục profile trong File Explorer."""
        try:
            if os.path.exists(self.chrome_profile_dir):
                os.startfile(self.chrome_profile_dir)
            else:
                QMessageBox.information(self, "Thông báo", "Thư mục Profile chưa tồn tại.")
        except Exception as e:
            QMessageBox.critical(self, "Lỗi", f"Không thể mở thư mục: {str(e)}")

class LoadingDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Data Assortment All in One")
        self.setFixedSize(300, 100)
        self.setWindowFlags(Qt.WindowType.Dialog | Qt.WindowType.FramelessWindowHint)
        
        layout = QVBoxLayout()
        
        # Label
        self.label = QLabel("Đang tải sheet...")
        self.label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.label)
        
        # Progress Bar
        self.progress = QProgressBar()
        self.progress.setTextVisible(False)
        self.progress.setRange(0, 100)
        self.progress.setValue(0)
        layout.addWidget(self.progress)
        
        self.setLayout(layout)
        
        # Animation
        self.animation = QPropertyAnimation(self.progress, b"value")
        self.animation.setDuration(1000)
        self.animation.setStartValue(0)
        self.animation.setEndValue(100)
        self.animation.setEasingCurve(QEasingCurve.Type.InOutSine)
        
        # Timer để reset animation
        self.timer = QTimer()
        self.timer.timeout.connect(self.reset_animation)
        self.timer.start(1100)  # Hơi lâu hơn duration để tránh giật

    def reset_animation(self):
        self.progress.setValue(0)
        self.animation.start()

    def closeEvent(self, event):
        self.timer.stop()
        self.animation.stop()
        super().closeEvent(event)

class ImageScrapingWidget(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Data Assortment All In One")
        self.worksheet = None
        self.error_count = 0
        self.processed_count = 0  # Thêm counter đã xử lý
        self.captcha_solved = False
        self.auth_paused = False  # Flag để theo dõi trạng thái auth như file logic
        # Thiết lập đường dẫn lưu trữ
        self.setup_storage_paths()
        self.init_ui()
        # Thay thế temp_results bằng SQLite manager
        self.data_manager = ScrapingDataManager()
        self.loading_dialog = None

        # Khởi tạo stealth instance
        self.stealth = Stealth()

        # Chạy quá trình dọn dẹp file cũ
        self.cleanup_old_logs()

        # Kiểm tra và tối ưu hóa Chrome profile
        self.optimize_chrome_profile()

    def setup_storage_paths(self):
        """Thiết lập các đường dẫn lưu trữ dữ liệu trong thư mục LOCAL APPDATA."""
        # Đường dẫn cơ sở là LOCAL APPDATA
        local_appdata = os.environ.get('LOCALAPPDATA')
        if not local_appdata:
            # Fallback nếu không lấy được LOCAL APPDATA
            local_appdata = os.path.join(os.path.expanduser('~'), 'AppData', 'Local')

        # Tạo thư mục Data All in One nếu chưa có
        data_dir = os.path.join(local_appdata, 'Data All in One')
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)

        # Tạo thư mục Image Scrape để lưu dữ liệu của ứng dụng
        img_scrape_dir = os.path.join(data_dir, 'Image Scrape')
        if not os.path.exists(img_scrape_dir):
            os.makedirs(img_scrape_dir)

        # Tạo thư mục logs để lưu các file log
        logs_dir = os.path.join(img_scrape_dir, 'logs')
        if not os.path.exists(logs_dir):
            os.makedirs(logs_dir)

        # Tạo thư mục Chrome Profile để lưu profile trình duyệt
        chrome_profile_dir = os.path.join(data_dir, 'Chrome Profile')
        if not os.path.exists(chrome_profile_dir):
            os.makedirs(chrome_profile_dir)
            print(f"Đã tạo thư mục Chrome Profile: {chrome_profile_dir}")

        # Lưu các đường dẫn
        self.base_dir = img_scrape_dir
        self.logs_dir = logs_dir
        self.chrome_profile_dir = chrome_profile_dir

        # Tên file log theo ngày
        today = datetime.datetime.now().strftime('%Y-%m-%d')
        self.log_file_path = os.path.join(self.logs_dir, f'image_results_{today}.txt')

    def cleanup_old_logs(self):
        """Xóa các file log cũ hơn 7 ngày."""
        try:
            # Lấy thời gian hiện tại
            now = datetime.datetime.now()

            # Tìm tất cả các file trong thư mục logs
            for file_path in Path(self.logs_dir).glob('*.txt'):
                # Lấy thời gian tạo file
                file_time = datetime.datetime.fromtimestamp(os.path.getctime(file_path))

                # Tính số ngày đã trôi qua
                days_old = (now - file_time).days

                # Nếu file cũ hơn 7 ngày, xóa đi
                if days_old > 7:
                    os.remove(file_path)
                    print(f"Đã xóa file log cũ: {file_path}")
        except Exception as e:
            print(f"Lỗi khi dọn dẹp file cũ: {str(e)}")

    def optimize_chrome_profile(self):
        """Kiểm tra và tối ưu hóa Chrome profile để lưu cache/cookies hiệu quả."""
        try:
            # Kiểm tra xem profile đã tồn tại chưa
            if os.path.exists(self.chrome_profile_dir):
                # Kiểm tra kích thước thư mục profile
                total_size = 0
                for dirpath, dirnames, filenames in os.walk(self.chrome_profile_dir):
                    for filename in filenames:
                        filepath = os.path.join(dirpath, filename)
                        try:
                            total_size += os.path.getsize(filepath)
                        except (OSError, IOError):
                            pass

                # Chuyển đổi sang MB
                size_mb = total_size / (1024 * 1024)
                print(f"Chrome Profile hiện tại: {size_mb:.1f} MB")

                # Nếu profile quá lớn (>500MB), dọn dẹp một số file tạm
                if size_mb > 500:
                    self.cleanup_chrome_profile()
                else:
                    print("Chrome Profile đã tồn tại và được tối ưu hóa")
            else:
                print("Chrome Profile chưa tồn tại, sẽ được tạo khi chạy lần đầu")

        except Exception as e:
            print(f"Lỗi khi kiểm tra Chrome Profile: {str(e)}")

    def cleanup_chrome_profile(self):
        """Dọn dẹp Chrome profile để giảm dung lượng nhưng giữ lại cookies/cache quan trọng."""
        try:
            print("Đang dọn dẹp Chrome Profile...")

            # Các thư mục có thể xóa an toàn để giảm dung lượng
            cleanup_dirs = [
                'BrowserMetrics',
                'CrashpadMetrics',
                'GraphiteDawnCache',
                'ShaderCache',
                'VideoDecodeStats',
                'optimization_guide_model_store',
                'optimization_guide_prediction_model_downloads'
            ]

            # Các file có thể xóa an toàn
            cleanup_files = [
                'LOG',
                'LOG.old',
                'MANIFEST-*',
                '*.tmp',
                '*.log'
            ]

            cleaned_size = 0

            # Xóa các thư mục không cần thiết
            for cleanup_dir in cleanup_dirs:
                dir_path = os.path.join(self.chrome_profile_dir, cleanup_dir)
                if os.path.exists(dir_path):
                    try:
                        dir_size = sum(os.path.getsize(os.path.join(dirpath, filename))
                                     for dirpath, dirnames, filenames in os.walk(dir_path)
                                     for filename in filenames)
                        shutil.rmtree(dir_path)
                        cleaned_size += dir_size
                        print(f"Đã xóa thư mục: {cleanup_dir}")
                    except Exception as e:
                        print(f"Không thể xóa {cleanup_dir}: {e}")

            # Xóa các file tạm
            for root, dirs, files in os.walk(self.chrome_profile_dir):
                for file in files:
                    if any(file.endswith(pattern.replace('*', '')) or
                          file.startswith(pattern.replace('*', ''))
                          for pattern in cleanup_files):
                        try:
                            file_path = os.path.join(root, file)
                            file_size = os.path.getsize(file_path)
                            os.remove(file_path)
                            cleaned_size += file_size
                        except Exception as e:
                            pass

            cleaned_mb = cleaned_size / (1024 * 1024)
            print(f"Đã dọn dẹp {cleaned_mb:.1f} MB từ Chrome Profile")

        except Exception as e:
            print(f"Lỗi khi dọn dẹp Chrome Profile: {str(e)}")



    def update_profile_status(self):
        """Cập nhật trạng thái profile ngắn gọn cho giao diện chính."""
        try:
            if os.path.exists(self.chrome_profile_dir):
                # Tính kích thước profile
                total_size = 0
                for dirpath, _, filenames in os.walk(self.chrome_profile_dir):
                    for filename in filenames:
                        filepath = os.path.join(dirpath, filename)
                        try:
                            total_size += os.path.getsize(filepath)
                        except (OSError, IOError):
                            pass

                size_mb = total_size / (1024 * 1024)

                # Kiểm tra cookies
                cookies_exist = os.path.exists(os.path.join(self.chrome_profile_dir, 'Default', 'Cookies'))

                # Thêm thông tin CloudflareBypasser
                cf_status = "🛡️ CF Bypass: ✅" if CLOUDFLARE_BYPASSER_AVAILABLE else "🛡️ CF Bypass: ❌"

                status_text = f"📁 Profile: {size_mb:.1f} MB | 🍪 {'Có cookies' if cookies_exist else 'Chưa có cookies'} | {cf_status}"
                self.profile_status_label.setText(status_text)
                self.profile_status_label.setStyleSheet("color: #2e7d32; font-weight: bold;")
            else:
                cf_status = "🛡️ CF Bypass: ✅" if CLOUDFLARE_BYPASSER_AVAILABLE else "🛡️ CF Bypass: ❌"
                self.profile_status_label.setText(f"📁 Profile: Chưa tạo | ⚠️ Sẽ tạo khi chạy lần đầu | {cf_status}")
                self.profile_status_label.setStyleSheet("color: #f57c00; font-weight: bold;")

        except Exception as e:
            self.profile_status_label.setText(f"❌ Lỗi: {str(e)}")
            self.profile_status_label.setStyleSheet("color: #d32f2f; font-weight: bold;")

    def open_profile_manager(self):
        """Mở dialog quản lý profile."""
        dialog = ProfileManagerDialog(self, self.chrome_profile_dir)
        dialog.exec()
        # Cập nhật lại trạng thái sau khi đóng dialog
        self.update_profile_status()

    def init_ui(self):
        central_layout = QVBoxLayout(self)
        
        # Add top bar with back button and context menu
        top_bar = QHBoxLayout()
        self.btn_back = QPushButton("⬅️ Trở về giao diện chính")
        self.btn_back.setFixedSize(150, 40)
        self.btn_back.setStyleSheet("border: none; font-size: 12px; font-weight: bold;")
        self.btn_back.clicked.connect(self.handle_back)
        top_bar.addWidget(self.btn_back, alignment=Qt.AlignmentFlag.AlignLeft)
        
        # Add context menu button
        self.context_button = QToolButton()
        self.context_button.setText("Chuyển đến ➡️")
        self.context_button.setFixedSize(100, 30)
        self.context_button.setStyleSheet("""
            QToolButton {
                border: none;
                background: transparent;
                font-size: 12px; 
                font-weight: bold;
            }
            QToolButton::menu-indicator {
                image: none;
                width: 0px;
            }
        """)
        self.context_button.installEventFilter(self)
        self.context_button.setPopupMode(QToolButton.ToolButtonPopupMode.InstantPopup)
        menu = QMenu(self.context_button)
        menu.addAction("AutoShopee Scraping", lambda: self.goto_other_program("autoshopee_scraping"))
        menu.addAction("Import Data", lambda: self.goto_other_program("import_data"))
        menu.addAction("Raw Data Processing", lambda: self.goto_other_program("raw_data_processing"))
        menu.addAction("Internal Data", lambda: self.goto_other_program("internal_data"))
        self.context_button.setMenu(menu)
        top_bar.addWidget(self.context_button, alignment=Qt.AlignmentFlag.AlignRight)
        
        central_layout.addLayout(top_bar)
        
        # Original layout
        main_layout = QVBoxLayout()

        # Google Sheet group
        sheet_group = QGroupBox("Google Sheet Info")
        sheet_layout = QVBoxLayout()

        # Sheet URL và Load button section
        url_layout = QHBoxLayout()
        self.sheet_link_edit = QLineEdit()
        self.sheet_link_edit.setPlaceholderText("Nhập link Google Sheet...")
        load_btn = QPushButton("Load Sheet")
        load_btn.clicked.connect(self.load_sheet)
        label1 = QLabel("Link Google Sheet:")
        label1.setFixedWidth(120)
        url_layout.addWidget(label1)
        url_layout.addWidget(self.sheet_link_edit, 1)
        url_layout.addWidget(load_btn)
        sheet_layout.addLayout(url_layout)

        # Sheet selection section
        sheet_select_layout = QHBoxLayout()
        self.sheet_combo = QComboBox()
        label2 = QLabel("Chọn Sheet:")
        label2.setFixedWidth(120)
        sheet_select_layout.addWidget(label2)
        sheet_select_layout.addWidget(self.sheet_combo, 1)
        sheet_layout.addLayout(sheet_select_layout)

        sheet_group.setLayout(sheet_layout)
        main_layout.addWidget(sheet_group)

        # Column mapping group
        mapping_group = QGroupBox("Column Mapping")
        mapping_layout = QVBoxLayout()

        # Status column mapping
        status_layout = QHBoxLayout()
        self.status_col_edit = QLineEdit("Y")  # Default column Y
        label3 = QLabel("Cột hình ảnh:")
        label3.setFixedWidth(120)
        status_layout.addWidget(label3)
        status_layout.addWidget(self.status_col_edit, 1)
        mapping_layout.addLayout(status_layout)

        # Product URL column mapping  
        url_col_layout = QHBoxLayout()
        self.url_col_edit = QLineEdit("BJ")  # Default column BJ
        label4 = QLabel("Cột link SKU:")
        label4.setFixedWidth(120)
        url_col_layout.addWidget(label4)
        url_col_layout.addWidget(self.url_col_edit, 1)
        mapping_layout.addLayout(url_col_layout)

        # Image URL column mapping
        img_col_layout = QHBoxLayout()
        self.img_col_edit = QLineEdit("BK")  # Default column BK
        label5 = QLabel("Cột link ảnh:")
        label5.setFixedWidth(120)
        img_col_layout.addWidget(label5)
        img_col_layout.addWidget(self.img_col_edit, 1)
        mapping_layout.addLayout(img_col_layout)

        mapping_group.setLayout(mapping_layout)
        main_layout.addWidget(mapping_group)

        # Results table
        results_group = QGroupBox("Processing Results")
        results_layout = QVBoxLayout()
        
        # Counter labels với processed counter ở góc phải
        counter_layout = QHBoxLayout()
        self.total_label = QLabel("Tổng số link cần xử lý: 0")
        self.error_label = QLabel("Số link lỗi: 0")
        self.processed_label = QLabel("Đã xử lý: 0")  # Thêm counter đã xử lý

        counter_layout.addWidget(self.total_label)
        counter_layout.addWidget(self.error_label)
        counter_layout.addStretch()  # Đẩy processed_label sang phải
        counter_layout.addWidget(self.processed_label)
        results_layout.addLayout(counter_layout)
        
        # Progress bar - chỉ hiển thị progress bar, không hiển thị số %
        progress_layout = QHBoxLayout()
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setTextVisible(False)  # Ẩn text % trên progress bar
        self.status_label = QLabel("")
        progress_layout.addWidget(self.progress_bar)
        # Không thêm status_label để không hiển thị số %
        results_layout.addLayout(progress_layout)
        
        # Table
        self.results_table = QTableWidget()
        self.results_table.setMinimumHeight(250)
        self.results_table.setColumnCount(4)
        self.results_table.setHorizontalHeaderLabels(["Dòng", "Link sản phẩm", "Trạng thái", "Link ảnh"])
        self.results_table.setColumnWidth(0, 60)      # Dòng - keep narrow
        self.results_table.setColumnWidth(1, 300)     # Link sản phẩm - slightly wider
        self.results_table.setColumnWidth(2, 140)     # Trạng thái - slightly narrower
        self.results_table.setColumnWidth(3, 300)     # Link ảnh - make equal to Link sản phẩm width
        self.results_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        results_layout.addWidget(self.results_table)
        
        results_group.setLayout(results_layout)
        main_layout.addWidget(results_group)

        # Profile management section
        profile_group = QGroupBox("Chrome Profile")
        profile_layout = QHBoxLayout()

        # Profile status label
        self.profile_status_label = QLabel()
        self.update_profile_status()
        profile_layout.addWidget(self.profile_status_label, 1)

        # Profile manager button
        profile_manager_btn = QPushButton("🗂 Quản lý Profile")
        profile_manager_btn.clicked.connect(self.open_profile_manager)
        profile_manager_btn.setFixedHeight(35)
        profile_manager_btn.setFixedWidth(150)
        # Áp dụng theme cho nút quản lý profile
        theme = get_theme_styles()
        profile_manager_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {theme['accent']};
                color: white;
                border: 1px solid {theme['accent']};
                border-radius: 5px;
                padding: 5px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: #106ebe;
            }}
            QPushButton:pressed {{
                background-color: #005a9e;
            }}
        """)

        profile_layout.addWidget(profile_manager_btn)

        profile_group.setLayout(profile_layout)
        main_layout.addWidget(profile_group)

        # Start and Stop buttons
        buttons_layout = QHBoxLayout()
        self.confirm_button = QPushButton("Bắt đầu xử lý")
        self.confirm_button.clicked.connect(self.start_processing)
        self.confirm_button.setEnabled(False)

        self.stop_button = QPushButton("Dừng xử lý")
        self.stop_button.clicked.connect(self.stop_processing)
        self.stop_button.setEnabled(False)

        # Thêm auth resume button như file logic
        self.auth_resume_button = QPushButton("OK - Đã giải captcha")
        self.auth_resume_button.clicked.connect(self.resume_after_auth)
        self.auth_resume_button.setEnabled(False)
        self.auth_resume_button.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold;")

        buttons_layout.addWidget(self.confirm_button)
        buttons_layout.addWidget(self.stop_button)
        buttons_layout.addWidget(self.auth_resume_button)
        main_layout.addLayout(buttons_layout)

        central_layout.addLayout(main_layout)
        
        # Initialize processing flag
        self.processing = False

    def closeEvent(self, event):
        """Cleanup khi đóng ứng dụng"""
        try:
            # Đóng SQLite connection
            if hasattr(self, 'data_manager'):
                self.data_manager.close()
        except Exception as e:
            print(f"Error during cleanup: {e}")
        finally:
            event.accept()

    def handle_back(self):
        if self.processing:
            reply = QMessageBox.question(
                self, 
                "Xác nhận", 
                "Bạn có muốn dừng quá trình scraping hiện tại và quay lại?",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            if reply == QMessageBox.StandardButton.Yes:
                self.processing = False
                # Cleanup if needed
                self.stop_processing()
                if hasattr(self, 'back_callback'):
                    self.back_callback()
        else:
            if hasattr(self, 'back_callback'):
                self.back_callback()

    def goto_other_program(self, program_name):
        # This will be connected from the main window
        pass
    
    def eventFilter(self, obj, event):
        if obj == self.context_button and event.type() == QEvent.Type.Enter:
            self.context_button.showMenu()
        return super().eventFilter(obj, event)

    def keyPressEvent(self, event):
        if event.key() == Qt.Key.Key_Backspace:
            self.handle_back()
        else:
            super().keyPressEvent(event)

    def stop_image_scraping(self):
        """Stop all image scraping operations safely"""
        self.processing = False
        # Reset UI
        self.confirm_button.setEnabled(True)
        self.stop_button.setEnabled(False)

    def load_sheet(self):
        try:
            sheet_link = self.sheet_link_edit.text().strip()
            if not sheet_link:
                QMessageBox.warning(self, "Lỗi", "Vui lòng nhập link Google Sheet.")
                return

            # Hiển thị loading dialog
            self.loading_dialog = LoadingDialog(self)
            self.loading_dialog.show()
            QApplication.processEvents()

            match = re.search(r"/d/([a-zA-Z0-9-_]+)", sheet_link)
            if not match:
                self.loading_dialog.close()
                QMessageBox.warning(self, "Lỗi", "Không tìm thấy Sheet ID trong link.")
                return
                
            sheet_id = match.group(1)
            gsm = GoogleSheetManager(auth_type='oauth', credentials_data=OAUTH_CREDENTIALS_B64)
            sh = gsm.open_by_key(sheet_id)
            
            # Load all worksheet names to combo box
            self.sheet_combo.clear()
            worksheet_list = sh.worksheets()
            for ws in worksheet_list:
                self.sheet_combo.addItem(ws.title)
            
            # Set default to "Deal list" if exists
            deal_list_index = self.sheet_combo.findText("Deal list")
            if deal_list_index >= 0:
                self.sheet_combo.setCurrentIndex(deal_list_index)
            
            self.confirm_button.setEnabled(True)
            
            # Đóng loading dialog
            if self.loading_dialog:
                self.loading_dialog.close()
                self.loading_dialog = None
                
            QMessageBox.information(self, "Thành công", "Đã load sheet thành công!")
            
        except Exception as e:
            if self.loading_dialog:
                self.loading_dialog.close()
                self.loading_dialog = None
            QMessageBox.critical(self, "Lỗi", f"Không thể load sheet: {str(e)}")

    def start_processing(self):
        try:
            print("🚀 start_processing called")
            self.sheet_link = self.sheet_link_edit.text().strip()
            if not self.sheet_link:
                QMessageBox.warning(self, "Lỗi", "Vui lòng nhập link Google Sheet.")
                return

            print(f"📋 Sheet link: {self.sheet_link}")
            self.confirm_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            self.processing = True

            print("🧵 Creating thread...")
            thread = Thread(target=self.process_sheet)
            thread.daemon = True  # Đảm bảo thread sẽ tự động dừng khi main app thoát
            print("🏃 Starting thread...")
            thread.start()
            print("✅ Thread started successfully")

            # Immediate UI feedback
            self.status_label.setText("🚀 Đang khởi động xử lý...")
            QApplication.processEvents()

        except Exception as e:
            print(f"❌ Error in start_processing: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "Lỗi", f"Không thể bắt đầu xử lý: {e}")
            # Reset UI state
            self.confirm_button.setEnabled(True)
            self.stop_button.setEnabled(False)
            self.processing = False

    def stop_processing(self):
        self.processing = False
        self.status_label.setText("Đang dừng...")
        self.stop_button.setEnabled(False)

        # Hiển thị dialog hỏi người dùng có muốn cập nhật các dữ liệu đã xử lý không
        QTimer.singleShot(500, self.show_update_dialog)

    def resume_after_auth(self):
        """Resume processing after authentication như file logic"""
        print("User clicked resume after auth")
        self.auth_paused = False
        self.captcha_solved = True  # Set captcha solved flag
        self.auth_resume_button.setEnabled(False)
        print("✅ Đã xác thực xong, đang tiếp tục")
        
    def show_update_dialog(self):
        """Hiển thị dialog hỏi người dùng có muốn cập nhật kết quả đã xử lý đến thời điểm tạm dừng không."""
        results_count = self.data_manager.get_results_count()
        if results_count == 0:
            return  # Không có kết quả nào để cập nhật
            
        dialog = QMessageBox(self)
        dialog.setIcon(QMessageBox.Icon.Question)
        dialog.setWindowTitle("Data Assortment All in One")
        dialog.setText(f"Đã dừng xử lý. Bạn có muốn cập nhật {results_count} kết quả đã xử lý lên Google Sheet không?")
        
        update_button = dialog.addButton("Cập nhật", QMessageBox.ButtonRole.YesRole)
        continue_button = dialog.addButton("Cập nhật và tiếp tục", QMessageBox.ButtonRole.ActionRole)
        skip_button = dialog.addButton("Bỏ qua", QMessageBox.ButtonRole.NoRole)
        
        dialog.exec()
        
        clicked_button = dialog.clickedButton()
        if clicked_button == update_button:
            self.update_partial_results(continue_processing=False)
        elif clicked_button == continue_button:
            self.update_partial_results(continue_processing=True)

    def update_partial_results(self, continue_processing=False):
        """Cập nhật các kết quả đã xử lý đến thời điểm tạm dừng lên Google Sheet."""
        results_count = self.data_manager.get_results_count()
        if results_count == 0:
            return
            
        try:
            self.update_ui_status("Đang cập nhật kết quả tạm thời...")
            
            # Lấy sheet và các thông số cần thiết
            sheet_name = self.sheet_combo.currentText()
            img_col = self.img_col_edit.text().strip()
            status_col = self.status_col_edit.text().strip()

            # Lấy sheet_link từ UI (vì self.sheet_link có thể chưa được set)
            sheet_link = self.sheet_link_edit.text().strip()
            if not sheet_link:
                raise ValueError("Không tìm thấy link Google Sheet")

            match = re.search(r"/d/([a-zA-Z0-9-_]+)", sheet_link)
            if not match:
                raise ValueError("Link Google Sheet không hợp lệ")
            sheet_id = match.group(1)
            gsm = GoogleSheetManager(auth_type='oauth', credentials_data=OAUTH_CREDENTIALS_B64)
            sh = gsm.open_by_key(sheet_id)
            worksheet = sh.worksheet(sheet_name)
            
            # Thêm dòng thông báo vào bảng kết quả
            row_count = self.results_table.rowCount()
            self.results_table.insertRow(row_count)
            self.results_table.setItem(row_count, 0, QTableWidgetItem(""))
            self.results_table.setItem(row_count, 1, QTableWidgetItem("")) 
            self.results_table.setItem(row_count, 2, QTableWidgetItem(f"Đang cập nhật {results_count} kết quả tạm thời..."))
            QApplication.processEvents()

            # Lấy tất cả kết quả từ SQLite (format như file logic: list of tuples)
            temp_results = self.data_manager.get_all_results()

            # Sử dụng cơ chế giống file logic: chunked processing với batch_data format
            chunk_size = 50
            chunks = [temp_results[i:i + chunk_size] for i in range(0, len(temp_results), chunk_size)]

            for chunk_idx, chunk in enumerate(chunks, 1):
                batch_data = []
                for row_num, img_url in chunk:
                    if img_url and not img_url.startswith(("Sản phẩm", "Timeout", "Lỗi", "Không")):
                        # Kiểm tra tuyệt đối không upload URL deo.shopeemobile.com
                        if 'deo.shopeemobile.com' in img_url:
                            print(f"🚫 Bỏ qua upload URL deo.shopeemobile.com dòng {row_num}: {img_url}")
                            continue

                        # Thêm dữ liệu vào batch dựa trên các trường đã được nhập
                        batch_data.append({"range": f"{img_col}{row_num}", "values": [[img_url]]})

                        # Cập nhật cột hình ảnh chỉ khi người dùng đã nhập trường này
                        if status_col:  # Nếu có nhập status_col
                            batch_data.append({"range": f"{status_col}{row_num}", "values": [[f'=IMAGE({img_col}{row_num})']]})

                if batch_data:
                    # Sử dụng cùng API call như file logic
                    worksheet.batch_update(batch_data, value_input_option="USER_ENTERED")

                # Update UI progress
                self.results_table.setItem(row_count, 2, QTableWidgetItem(f"Đã cập nhật chunk {chunk_idx}/{len(chunks)}..."))
                QApplication.processEvents()

            # Final update
            self.results_table.setItem(row_count, 2, QTableWidgetItem(f"Đã cập nhật {len(temp_results)} kết quả tạm thời lên Google Sheet"))
            self.results_table.setItem(row_count, 3, QTableWidgetItem("Hoàn tất"))

            # Lưu kết quả vào file như file logic
            result_dict = {row: url for row, url in temp_results}
            self.save_results_to_file(result_dict)

            if continue_processing:
                self.show_message("Cập nhật thành công", f"Đã cập nhật {results_count} kết quả tạm thời lên Google Sheet. Tiếp tục xử lý...")
                # Không xóa kết quả tạm thời vì sẽ tiếp tục xử lý
                # Khởi động lại quá trình xử lý
                self.processing = True
                self.stop_button.setEnabled(True)
                thread = Thread(target=self.resume_processing)
                thread.start()
            else:
                self.show_message("Cập nhật thành công", f"Đã cập nhật {results_count} kết quả tạm thời lên Google Sheet.")
                # Xóa kết quả tạm thời sau khi đã cập nhật
                self.data_manager.clear_all()
                # Enable lại button để có thể bắt đầu xử lý mới
                self.confirm_button.setEnabled(True)
                self.stop_button.setEnabled(False)


                
        except Exception as ex:
            self.update_ui_status(f"Lỗi: {str(ex)}")
            self.show_message("Lỗi", f"Có lỗi khi cập nhật kết quả tạm thời: {ex}")
            # Enable lại buttons khi có lỗi
            self.confirm_button.setEnabled(True)
            self.stop_button.setEnabled(False)
    
    def resume_processing(self):
        """Tiếp tục xử lý các dòng còn lại sau khi tạm dừng."""
        try:
            # Lấy thông tin cấu hình
            sheet_name = self.sheet_combo.currentText()
            url_col = self.url_col_edit.text().strip()
            img_col = self.img_col_edit.text().strip()
            status_col = self.status_col_edit.text().strip()
            
            # Convert column letters to indices
            url_idx = self.col2num(url_col) - 1
            status_idx = self.col2num(status_col) - 1
            
            # Mở lại sheet
            match = re.search(r"/d/([a-zA-Z0-9-_]+)", self.sheet_link)
            sheet_id = match.group(1)
            gsm = GoogleSheetManager(auth_type='oauth', credentials_data=OAUTH_CREDENTIALS_B64)
            sh = gsm.open_by_key(sheet_id)
            worksheet = sh.worksheet(sheet_name)
            
            # Lấy tất cả dữ liệu
            all_values = worksheet.get_all_values()
            
            # Tìm các dòng chưa xử lý (là những dòng có #N/A và không có trong temp_results)
            rows_to_process = []
            for idx, row in enumerate(all_values, start=1):
                if idx < 4:  # Bỏ qua 3 dòng đầu
                    continue
                # Kiểm tra xem row đã được xử lý chưa từ SQLite
                cursor = self.data_manager.conn.cursor()
                cursor.execute('SELECT COUNT(*) FROM scraping_results WHERE row_num = ?', (idx,))
                already_processed = cursor.fetchone()[0] > 0

                if (len(row) >= status_idx + 1 and row[status_idx].strip() == "#N/A" and
                    not already_processed and
                    len(row) >= url_idx + 1):
                    product_link = row[url_idx].strip()
                    rows_to_process.append((idx, product_link))
            
            if not rows_to_process:
                self.show_message("Thông báo", "Không còn dòng nào cần xử lý.")
                self.confirm_button.setEnabled(True)
                self.stop_button.setEnabled(False)
                self.processing = False
                return
                
            # Cập nhật tổng số lượng và bảng kết quả
            total_count = len(rows_to_process)
            self.total_label.setText(f"Tổng số link cần xử lý: {total_count}")
            
            # Xóa bảng cũ và tạo bảng mới
            self.results_table.setRowCount(total_count)
            for i, (row_num, product_link) in enumerate(rows_to_process):
                self.results_table.setItem(i, 0, QTableWidgetItem(str(row_num)))
                self.results_table.setItem(i, 1, QTableWidgetItem(product_link))
                self.results_table.setItem(i, 2, QTableWidgetItem("Đang chờ xử lý..."))
                self.results_table.setItem(i, 3, QTableWidgetItem(""))
            
            # Tiếp tục xử lý với các dòng còn lại
            results = {}  # Kết quả mới

            # Sử dụng DrissionPage
            page_manager = DrissionPageManager(self.chrome_profile_dir, headless=False)
            page = page_manager.create_page()

            if not page:
                self.show_message("Lỗi", "Không thể khởi tạo trình duyệt!")
                return

            try:
                for idx, (row_num, product_link) in enumerate(rows_to_process):
                    if not self.processing:
                        self.update_ui_status("Đã dừng xử lý")
                        break
                        
                    # Update progress
                    progress = int((idx / total_count) * 100)
                    self.update_ui_status("", progress)
                    
                    print(f"\n===== Tiếp tục xử lý dòng {row_num} với link sản phẩm: {product_link} =====")
                    
                    # Tiếp tục logic xử lý tương tự như trong process_sheet
                    # ...
                    
                    # (Sao chép phần xử lý từ process_sheet vào đây)
                    table_row = idx
                    status_item = self.results_table.item(table_row, 2)
                    if status_item:  # Safety check
                        status_item.setText("Đang xử lý...")
                    # Direct processEvents để tránh QBasicTimer error trong threads
                    QApplication.processEvents()
                    
                    # Tải trang với retry, check captcha/đăng nhập/cloudflare
                    try:
                        status = self.load_page_with_retry(page, product_link)
                    except Exception as e:
                        print(f"[Dòng {row_num}] Lỗi khi tải trang: {e}")
                        results[row_num] = f"Lỗi khi tải trang: {e}"
                        self.data_manager.add_result(row_num, product_link, None, "error", f"Lỗi khi tải trang: {e}")
                        if status_item:
                            status_item.setText("Lỗi tải trang")
                        self.error_count += 1
                        self.error_label.setText(f"Số link lỗi: {self.error_count}")
                        self.update_processed_count()
                        continue

                    if status == "cloudflare":
                        print(f"[Dòng {row_num}] Cloudflare challenge đã được bypass tự động.")
                        status_item.setText("Đã bypass Cloudflare")
                        QApplication.processEvents()
                        # Tiếp tục xử lý bình thường
                    elif status in ("captcha", "login"):
                        # (Sao chép phần xử lý captcha từ process_sheet vào đây)
                        # ...
                        print(f"[Dòng {row_num}] Yêu cầu captcha/đăng nhập.")
                        self.captcha_solved = False
                        status_item.setText("Đang chờ giải captcha...")
                        QApplication.processEvents()
                        
                        # Hiển thị dialog trực tiếp để tránh QBasicTimer error
                        self.show_captcha_dialog_non_blocking()
                        
                        # Chờ captcha được giải (không timeout)
                        while not self.captcha_solved:
                            QApplication.processEvents()  # Giữ UI responsive
                            import time as time_module
                            time_module.sleep(0.5)  # Ngủ ngắn để không chiếm CPU
                        
                        time_module.sleep(2)
                        page.reload()
                        time_module.sleep(3)
                        QApplication.processEvents()
                        
                        if "verify/captcha" in page.url or "buyer/login" in page.url:
                            print(f"[Dòng {row_num}] Sản phẩm này yêu cầu captcha/đăng nhập và vẫn chưa được giải.")
                            results[row_num] = "Sản phẩm này yêu cầu captcha/đăng nhập"
                            self.data_manager.add_result(row_num, product_link, None, "error", "Sản phẩm này yêu cầu captcha/đăng nhập")
                            status_item.setText("Yêu cầu captcha/đăng nhập")
                            self.results_table.setItem(table_row, 3, QTableWidgetItem("N/A"))
                            self.error_count += 1
                            self.error_label.setText(f"Số link lỗi: {self.error_count}")
                            self.update_processed_count()  # Cập nhật counter
                            page.close()
                            continue
                        else:
                            print(f"[Dòng {row_num}] Người dùng đã giải captcha, tiếp tục scrape.")
                            status = self.load_page_with_retry(page, product_link)
                            if status == "cloudflare":
                                print(f"[Dòng {row_num}] Cloudflare đã được bypass sau captcha.")
                                # Tiếp tục xử lý bình thường
                            elif status in ("captcha", "login"):
                                print(f"[Dòng {row_num}] Vẫn gặp captcha/login, bỏ qua link này.")
                                results[row_num] = "Sản phẩm này yêu cầu captcha/đăng nhập"
                                self.data_manager.add_result(row_num, product_link, None, "error", "Sản phẩm này yêu cầu captcha/đăng nhập")
                                status_item.setText("Yêu cầu captcha/đăng nhập")
                                self.results_table.setItem(table_row, 3, QTableWidgetItem("N/A"))
                                self.error_count += 1
                                self.error_label.setText(f"Số link lỗi: {self.error_count}")
                                self.update_processed_count()  # Cập nhật counter
                                continue
                    
                    # (Sao chép các phần xử lý khác từ process_sheet vào đây)
                    # ...
                    # Kiểm tra xem trang có thông báo "Sản phẩm này không tồn tại" hay không
                    try:
                        # Safe check với timeout để tránh UI freeze
                        page_content = page.html
                        if "Sản phẩm này không tồn tại" in page_content:
                            print(f"[Dòng {row_num}] Sản phẩm này không tồn tại")
                            results[row_num] = "Sản phẩm này không tồn tại"
                            self.data_manager.add_result(row_num, product_link, None, "error", "Sản phẩm này không tồn tại")

                            # Direct UI updates - không dùng QTimer trong thread
                            if status_item:
                                status_item.setText("Sản phẩm không tồn tại")
                            self.results_table.setItem(table_row, 3, QTableWidgetItem("N/A"))

                            self.error_count += 1
                            self.error_label.setText(f"Số link lỗi: {self.error_count}")
                            self.update_processed_count()
                            continue
                    except Exception as e:
                        print(f"[Dòng {row_num}] Lỗi khi kiểm tra trang: {e}")
                        # Tiếp tục xử lý thay vì crash

                    # Bỏ scroll để không làm khó giải captcha
                    # self.random_scroll(page)  # Disabled

                    try:
                        # Tối ưu: Giảm thời gian chờ load
                        import time as time_module  # Import explicit để tránh conflict
                        time_module.sleep(1)  # Giảm từ 2s xuống 1s
                        valid_src = self.find_valid_image_src(page)
                        if valid_src:
                            print(f"[Dòng {row_num}] Link ảnh: {valid_src}")
                            results[row_num] = valid_src
                            self.data_manager.add_result(row_num, product_link, valid_src, "success")
                            status_item.setText("Thành công")
                            self.results_table.setItem(table_row, 3, QTableWidgetItem(valid_src))
                        else:
                            print(f"[Dòng {row_num}] Không tìm thấy ảnh hợp lệ.")
                            results[row_num] = "Không tìm thấy ảnh hợp lệ"
                            self.data_manager.add_result(row_num, product_link, None, "error", "Không tìm thấy ảnh hợp lệ")
                            status_item.setText("Không tìm thấy ảnh")
                            self.results_table.setItem(table_row, 3, QTableWidgetItem("N/A"))
                            self.error_count += 1
                            self.error_label.setText(f"Số link lỗi: {self.error_count}")
                    except Exception as e:
                        print(f"[Dòng {row_num}] Lỗi khi tìm ảnh: {e}")
                        results[row_num] = f"Lỗi khi tìm ảnh: {e}"
                        self.data_manager.add_result(row_num, product_link, None, "error", f"Lỗi khi tìm ảnh: {e}")
                        status_item.setText(f"Lỗi: {str(e)}")
                        self.results_table.setItem(table_row, 3, QTableWidgetItem("N/A"))
                        self.error_count += 1
                        self.error_label.setText(f"Số link lỗi: {self.error_count}")

                    # Cập nhật counter đã xử lý cho mọi trường hợp
                    self.update_processed_count()
                    
                    # Random delay để tránh bị phát hiện bot (2-4s)
                    import random
                    delay = random.uniform(2, 3)
                    print(f"Delay {delay:.1f}s trước link tiếp theo...")
                    time_module.sleep(delay)
                    QApplication.processEvents()  # Giữ UI phản hồi

                    # DrissionPage không cần đóng tab riêng lẻ
                    pass

            finally:
                # Đóng DrissionPage
                page_manager.close()
            
            # Khi hoàn thành, cập nhật tất cả kết quả và xóa kết quả tạm thời
            if self.processing and results:
                # Kiểm tra an toàn kiểu dữ liệu của results
                if not isinstance(results, dict):
                    print(f"ERROR: results không phải dictionary trong resume_processing, là {type(results)}")
                    print(f"results content: {results}")
                    return

                # (Sao chép phần cập nhật Google Sheet từ process_sheet vào đây)
                # ...
                self.update_ui_status("Đang cập nhật Google Sheet...")
                update_requests = []
                image_formula_updates = []
                
                # Thêm dòng thông báo vào bảng kết quả
                row_count = self.results_table.rowCount()
                self.results_table.insertRow(row_count)
                self.results_table.setItem(row_count, 0, QTableWidgetItem(""))
                self.results_table.setItem(row_count, 1, QTableWidgetItem("")) 
                self.results_table.setItem(row_count, 2, QTableWidgetItem("Đang cập nhật Google Sheet..."))
                
                # Debug: In ra kiểu dữ liệu của results
                print(f"DEBUG: results type = {type(results)}")
                print(f"DEBUG: results content = {results}")

                # Kiểm tra xem results có phải là dictionary không
                if not isinstance(results, dict):
                    print(f"ERROR: results không phải dictionary, là {type(results)}")
                    return

                # Tạo batch update requests cho cả hai cột
                for row_num, img_url in results.items():
                    # Debug: In ra từng item
                    print(f"DEBUG: Processing row_num={row_num} (type: {type(row_num)}), img_url={img_url} (type: {type(img_url)})")

                    # Kiểm tra kiểu dữ liệu an toàn
                    if not isinstance(row_num, (int, str)) or not isinstance(img_url, str):
                        print(f"Lỗi dữ liệu: row_num={row_num} (type: {type(row_num)}), img_url={img_url} (type: {type(img_url)})")
                        continue

                    # Kiểm tra tuyệt đối không upload URL deo.shopeemobile.com
                    if 'deo.shopeemobile.com' in img_url:
                        print(f"🚫 Bỏ qua upload URL deo.shopeemobile.com dòng {row_num}: {img_url}")
                        continue

                    # Cập nhật URL ảnh ở cột img_col (BK hoặc cột được chỉ định)
                    cell_range = f"{img_col}{row_num}"
                    update_requests.append({
                        "range": cell_range,
                        "values": [[str(img_url)]]  # Đảm bảo là string
                    })
                    
                    # Nếu là URL ảnh hợp lệ (không phải thông báo lỗi), thêm công thức IMAGE vào cột status_col (Y)
                    if not img_url.startswith("Sản phẩm") and not img_url.startswith("Lỗi") and not img_url.startswith("Không"):
                        formula = f'=IMAGE({img_col}{row_num})'  # Tham chiếu đến ô chứa URL thay vì nhập trực tiếp URL
                        cell_range_status = f"{status_col}{row_num}"
                        image_formula_updates.append({
                            "range": cell_range_status,
                            "values": [[formula]]
                        })
                
                # Log thông tin update
                sheet_update_row = row_count
                self.results_table.setItem(sheet_update_row, 2, QTableWidgetItem(f"Cập nhật {len(update_requests)} dòng..."))
                QApplication.processEvents()
                
                # Upload trực tiếp trong main thread như file logic (không dùng threading)
                try:
                    # Tạo batch data cho cả URL và IMAGE formula
                    all_batch_data = []

                    # Thêm URL updates
                    if update_requests:
                        all_batch_data.extend(update_requests)

                    # Thêm IMAGE formula updates
                    if image_formula_updates:
                        all_batch_data.extend(image_formula_updates)

                    # Process in chunks để tránh API limits và UI freezing
                    if all_batch_data:
                        total_chunks = (len(all_batch_data) + 49) // 50  # Ceiling division by 50
                        self.results_table.setItem(sheet_update_row, 2,
                                                QTableWidgetItem(f"Đang cập nhật Google Sheet (0/{total_chunks} chunks)..."))

                        for i in range(0, len(all_batch_data), 50):
                            # Check if processing was stopped during upload
                            if not self.processing:
                                self.results_table.setItem(sheet_update_row, 2, QTableWidgetItem("Đã dừng cập nhật"))
                                break

                            chunk = all_batch_data[i:i+50]
                            chunk_num = (i // 50) + 1
                            self.results_table.setItem(sheet_update_row, 2,
                                                    QTableWidgetItem(f"Đang cập nhật Google Sheet ({chunk_num}/{total_chunks} chunks)..."))

                            # Allow UI to update before each chunk
                            QApplication.processEvents()

                            # Upload chunk
                            worksheet.batch_update(chunk, value_input_option="USER_ENTERED")

                            # Allow UI to update after each chunk
                            QApplication.processEvents()

                        # Final UI update
                        self.results_table.setItem(sheet_update_row, 2,
                                                QTableWidgetItem(f"Đã cập nhật {len(update_requests)} dòng lên Google Sheet"))
                        self.results_table.setItem(sheet_update_row, 3, QTableWidgetItem("Hoàn tất"))
                        QApplication.processEvents()

                except Exception as upload_error:
                    print(f"Lỗi khi cập nhật Google Sheet: {upload_error}")
                    self.results_table.setItem(sheet_update_row, 2, QTableWidgetItem(f"Lỗi: {str(upload_error)}"))
                    QApplication.processEvents()
                
                self.progress_bar.setValue(100)
                self.update_ui_status("", 100)  # 100%
                self.show_message("Hoàn thành", "Đã xử lý xong và cập nhật Google Sheet.")
                
                # Xóa kết quả tạm thời vì đã cập nhật đầy đủ
                self.data_manager.clear_all()
                
                # Lưu kết quả vào file logs để backup
                self.save_results_to_file(results)
            
        except Exception as ex:
            self.update_ui_status(f"Lỗi: {str(ex)}")
            self.show_message("Lỗi", f"Có lỗi khi tiếp tục xử lý: {ex}")
        finally:
            self.confirm_button.setEnabled(True)
            self.stop_button.setEnabled(False)
            self.processing = False

    def update_processed_count(self):
        """Cập nhật counter đã xử lý với safe UI update"""
        self.processed_count += 1
        self.processed_label.setText(f"Đã xử lý: {self.processed_count}")
        # Direct processEvents - không dùng QTimer trong thread
        QApplication.processEvents()

    def update_ui_status(self, message, percent=None):
        if percent is not None:
            self.status_label.setText(f"{percent}%")
            self.progress_bar.setValue(percent)
        else:
            # Chỉ hiển thị một số thông báo đặc biệt
            if message.startswith("Đã dừng") or message.startswith("Lỗi"):
                self.status_label.setText(message)
            # Các thông báo khác không hiển thị
        QApplication.processEvents()

    def process_sheet(self):
        try:
            print("🔄 process_sheet started")
            # Immediate UI feedback
            self.update_ui_status("🔄 Đang khởi tạo...")

            # Reset counters and temporary results
            self.error_count = 0
            self.processed_count = 0  # Reset counter đã xử lý
            self.data_manager.clear_all()  # Xóa kết quả tạm thời cũ
            print("📊 Counters reset, SQLite cleared")
            self.results_table.setRowCount(0)
            self.progress_bar.setValue(0)
            self.status_label.setText("")  # Không hiển thị "0%" ban đầu
            self.processed_label.setText("Đã xử lý: 0")  # Reset processed label
            
            # Lấy sheet được chọn
            sheet_name = self.sheet_combo.currentText()
            # Lấy các cột mapping
            status_col = self.status_col_edit.text().strip()
            url_col = self.url_col_edit.text().strip()
            img_col = self.img_col_edit.text().strip()

            # Convert column letters to indices
            status_idx = self.col2num(status_col) - 1
            url_idx = self.col2num(url_col) - 1
            img_idx = self.col2num(img_col) - 1

            # --- Tiếp tục logic cũ nhưng sử dụng các index mới ---
            match = re.search(r"/d/([a-zA-Z0-9-_]+)", self.sheet_link)
            sheet_id = match.group(1)
            gsm = GoogleSheetManager(auth_type='oauth', credentials_data=OAUTH_CREDENTIALS_B64)
            sh = gsm.open_by_key(sheet_id)
            worksheet = sh.worksheet(sheet_name)

            self.update_ui_status("Đang quét các dòng cần xử lý...")
            all_values = worksheet.get_all_values()
            # Quét từ dòng thứ 4 trở đi (dòng 1-3 là tiêu đề)
            rows_to_process = []
            for idx, row in enumerate(all_values, start=1):
                if idx < 4:
                    continue
                # Kiểm tra giá trị "#N/A" ở cột status
                if len(row) >= status_idx + 1 and row[status_idx].strip() == "#N/A":
                    # Lấy link sản phẩm
                    if len(row) >= url_idx + 1:
                        product_link = row[url_idx].strip()
                        rows_to_process.append((idx, product_link))
            
            if not rows_to_process:
                self.progress_bar.setValue(0)
                # Không hiển thị "0%" khi không có dòng
                self.show_message("Thông báo", "Không có dòng nào cần xử lý.")
                self.confirm_button.setEnabled(True)
                self.stop_button.setEnabled(False)
                return

            # Update total count
            self.total_label.setText(f"Tổng số link cần xử lý: {len(rows_to_process)}")
            
            # Prepare table
            self.results_table.setRowCount(len(rows_to_process))
            for i, (row_num, product_link) in enumerate(rows_to_process):
                self.results_table.setItem(i, 0, QTableWidgetItem(str(row_num)))
                self.results_table.setItem(i, 1, QTableWidgetItem(product_link))
                self.results_table.setItem(i, 2, QTableWidgetItem("Đang chờ xử lý..."))
                self.results_table.setItem(i, 3, QTableWidgetItem(""))

            # Update progress at start
            self.update_ui_status("", 0)  # Hiển thị 0% khi bắt đầu

            # --- 2. Scrape ảnh từ Shopee cho các dòng cần xử lý ---
            results = {}  # key: row number, value: scraped image URL
            self.update_ui_status("Đang chuẩn bị trình duyệt...")

            # Sử dụng DrissionPage thay vì Playwright
            self.update_ui_status("🌐 Đang khởi tạo trình duyệt...")
            page_manager = DrissionPageManager(self.chrome_profile_dir, headless=False)
            page = page_manager.create_page()

            if not page:
                self.update_ui_status("❌ Lỗi khởi tạo trình duyệt")
                self.show_message("Lỗi", "Không thể khởi tạo trình duyệt!")
                return

            self.update_ui_status("✅ Trình duyệt đã sẵn sàng, bắt đầu xử lý...")

            captcha_solved_globally = False  # Biến theo dõi xem captcha đã được giải quyết hay chưa

            try:
                import time as time_module  # Import explicit để tránh conflict
                total_rows = len(rows_to_process)
                for idx, (row_num, product_link) in enumerate(rows_to_process):
                    if not self.processing:
                        self.update_ui_status("Đã dừng xử lý")
                        break

                    # Update progress
                    progress = int((idx / total_rows) * 100)
                    self.progress_bar.setValue(progress)
                    self.update_ui_status("", progress)  # Chỉ cập nhật phần trăm

                    print(f"\n===== Xử lý dòng {row_num} với link sản phẩm: {product_link} =====")

                    table_row = idx
                    status_item = self.results_table.item(table_row, 2)
                    if status_item:  # Safety check
                        status_item.setText("Đang xử lý...")
                    # Direct processEvents để tránh QBasicTimer error trong threads
                    QApplication.processEvents()
                    
                    # Tải trang với retry, check captcha/đăng nhập/cloudflare
                    status = self.load_page_with_retry(page, product_link)

                    if status == "cloudflare":
                        print(f"[Dòng {row_num}] Cloudflare challenge đã được bypass tự động.")
                        status_item.setText("Đã bypass Cloudflare")
                        QApplication.processEvents()
                        # Tiếp tục xử lý bình thường
                    elif status in ("captcha", "login"):
                        print(f"[Dòng {row_num}] Yêu cầu captcha/đăng nhập.")
                        self.captcha_solved = False
                        status_item.setText("Đang chờ giải captcha...")
                        QApplication.processEvents()
                        
                        # Hiển thị dialog trực tiếp để tránh QBasicTimer error
                        self.show_captcha_dialog_non_blocking()
                        
                        # Chờ captcha được giải (không timeout)
                        while not self.captcha_solved:
                            QApplication.processEvents()  # Giữ UI responsive
                            time_module.sleep(0.5)  # Ngủ ngắn để không chiếm CPU
                        
                        time_module.sleep(2)
                        page.refresh()  # DrissionPage method
                        time_module.sleep(3)
                        QApplication.processEvents()

                        if "verify/captcha" in page.url or "buyer/login" in page.url:
                            print(f"[Dòng {row_num}] Sản phẩm này yêu cầu captcha/đăng nhập và vẫn chưa được giải.")
                            results[row_num] = "Sản phẩm này yêu cầu captcha/đăng nhập"
                            self.data_manager.add_result(row_num, product_link, None, "error", "Sản phẩm này yêu cầu captcha/đăng nhập")
                            status_item.setText("Yêu cầu captcha/đăng nhập")
                            self.results_table.setItem(table_row, 3, QTableWidgetItem("N/A"))
                            self.error_count += 1
                            self.error_label.setText(f"Số link lỗi: {self.error_count}")
                            self.update_processed_count()  # Cập nhật counter
                            continue
                        else:
                            print(f"[Dòng {row_num}] Người dùng đã giải captcha, tiếp tục scrape.")
                            captcha_solved_globally = True  # Đánh dấu đã giải captcha
                            status = self.load_page_with_retry(page, product_link)
                            if status == "cloudflare":
                                print(f"[Dòng {row_num}] Cloudflare đã được bypass sau captcha.")
                                # Tiếp tục xử lý bình thường
                            elif status in ("captcha", "login"):
                                print(f"[Dòng {row_num}] Vẫn gặp captcha/login, bỏ qua link này.")
                                results[row_num] = "Sản phẩm này yêu cầu captcha/đăng nhập"
                                self.data_manager.add_result(row_num, product_link, None, "error", "Sản phẩm này yêu cầu captcha/đăng nhập")
                                status_item.setText("Yêu cầu captcha/đăng nhập")
                                self.results_table.setItem(table_row, 3, QTableWidgetItem("N/A"))
                                self.error_count += 1
                                self.error_label.setText(f"Số link lỗi: {self.error_count}")
                                self.update_processed_count()  # Cập nhật counter
                                page.close()
                                continue
                    
                    # Kiểm tra xem trang có thông báo "Sản phẩm này không tồn tại" hay không
                    try:
                        # Safe check với timeout để tránh UI freeze
                        page_content = page.html
                        if "Sản phẩm này không tồn tại" in page_content:
                            print(f"[Dòng {row_num}] Sản phẩm này không tồn tại")
                            results[row_num] = "Sản phẩm này không tồn tại"
                            self.data_manager.add_result(row_num, product_link, None, "error", "Sản phẩm này không tồn tại")

                            # Direct UI updates - không dùng QTimer trong thread
                            if status_item:
                                status_item.setText("Sản phẩm không tồn tại")
                            self.results_table.setItem(table_row, 3, QTableWidgetItem("N/A"))

                            self.error_count += 1
                            self.error_label.setText(f"Số link lỗi: {self.error_count}")
                            self.update_processed_count()
                            continue
                    except Exception as e:
                        print(f"[Dòng {row_num}] Lỗi khi kiểm tra trang: {e}")
                        # Tiếp tục xử lý thay vì crash

                    # Bỏ scroll để không làm khó giải captcha
                    # self.random_scroll(page)  # Disabled

                    try:
                        # Tối ưu: Giảm thời gian chờ load
                        time_module.sleep(1)  # Giảm từ 2s xuống 1s
                        valid_src = self.find_valid_image_src(page)
                        if valid_src:
                            print(f"[Dòng {row_num}] Link ảnh: {valid_src}")
                            results[row_num] = valid_src
                            self.data_manager.add_result(row_num, product_link, valid_src, "success")
                            status_item.setText("Thành công")
                            self.results_table.setItem(table_row, 3, QTableWidgetItem(valid_src))
                        else:
                            print(f"[Dòng {row_num}] Không tìm thấy ảnh hợp lệ.")
                            results[row_num] = "Không tìm thấy ảnh hợp lệ"
                            self.data_manager.add_result(row_num, product_link, None, "error", "Không tìm thấy ảnh hợp lệ")
                            status_item.setText("Không tìm thấy ảnh")
                            self.results_table.setItem(table_row, 3, QTableWidgetItem("N/A"))
                            self.error_count += 1
                            self.error_label.setText(f"Số link lỗi: {self.error_count}")
                    except Exception as e:
                        print(f"[Dòng {row_num}] Lỗi khi tìm ảnh: {e}")
                        results[row_num] = f"Lỗi khi tìm ảnh: {e}"
                        self.data_manager.add_result(row_num, product_link, None, "error", f"Lỗi khi tìm ảnh: {e}")
                        status_item.setText(f"Lỗi: {str(e)}")
                        self.results_table.setItem(table_row, 3, QTableWidgetItem("N/A"))
                        self.error_count += 1
                        self.error_label.setText(f"Số link lỗi: {self.error_count}")

                    # Cập nhật counter đã xử lý cho mọi trường hợp
                    self.update_processed_count()

                    # Random delay để tránh bị phát hiện bot (2-4s)
                    import random
                    delay = random.uniform(2, 3)
                    print(f"Delay {delay:.1f}s trước link tiếp theo...")
                    time_module.sleep(delay)
                    QApplication.processEvents()  # Giữ UI phản hồi

            finally:
                # Đóng DrissionPage
                page_manager.close()

            # --- 3. Batch update Google Sheet ---
            if not self.processing:
                # Khi đã dừng, không tự động cập nhật nhưng lưu lại kết quả tạm thời
                self.update_ui_status("Đã dừng, không cập nhật Sheet")
            else:
                # Nếu xử lý hoàn tất, cập nhật tất cả kết quả và xóa kết quả tạm thời
                if results:
                    # Kiểm tra an toàn kiểu dữ liệu của results
                    if not isinstance(results, dict):
                        print(f"ERROR: results không phải dictionary trong process_sheet, là {type(results)}")
                        print(f"results content: {results}")
                        return

                    self.update_ui_status("Đang cập nhật Google Sheet...")
                    update_requests = []
                    image_formula_updates = []
                    
                    # Thêm dòng thông báo vào bảng kết quả
                    row_count = self.results_table.rowCount()
                    self.results_table.insertRow(row_count)
                    self.results_table.setItem(row_count, 0, QTableWidgetItem(""))
                    self.results_table.setItem(row_count, 1, QTableWidgetItem("")) 
                    self.results_table.setItem(row_count, 2, QTableWidgetItem("Đang cập nhật Google Sheet..."))
                    
                    # Debug: In ra kiểu dữ liệu của results
                    print(f"DEBUG: results type = {type(results)}")
                    print(f"DEBUG: results content = {results}")

                    # Convert results to dict nếu là list (từ SQLite)
                    if isinstance(results, list):
                        results_dict = dict(results)
                        print(f"DEBUG: Converted list to dict: {results_dict}")
                    elif isinstance(results, dict):
                        results_dict = results
                    else:
                        print(f"ERROR: results không phải dictionary hoặc list, là {type(results)}")
                        return

                    # Tạo batch update requests cho cả hai cột
                    for row_num, img_url in results_dict.items():
                        # Debug: In ra từng item
                        print(f"DEBUG: Processing row_num={row_num} (type: {type(row_num)}), img_url={img_url} (type: {type(img_url)})")

                        # Kiểm tra kiểu dữ liệu an toàn
                        if not isinstance(row_num, (int, str)) or not isinstance(img_url, str):
                            print(f"Lỗi dữ liệu: row_num={row_num} (type: {type(row_num)}), img_url={img_url} (type: {type(img_url)})")
                            continue

                        # Kiểm tra tuyệt đối không upload URL deo.shopeemobile.com
                        if 'deo.shopeemobile.com' in img_url:
                            print(f"🚫 Bỏ qua upload URL deo.shopeemobile.com dòng {row_num}: {img_url}")
                            continue

                        # Cập nhật URL ảnh ở cột img_col (BK hoặc cột được chỉ định)
                        cell_range = f"{img_col}{row_num}"
                        update_requests.append({
                            "range": cell_range,
                            "values": [[str(img_url)]]  # Đảm bảo là string
                        })
                        
                        # Nếu là URL ảnh hợp lệ (không phải thông báo lỗi), thêm công thức IMAGE vào cột status_col (Y)
                        if not img_url.startswith("Sản phẩm") and not img_url.startswith("Lỗi") and not img_url.startswith("Không"):
                            formula = f'=IMAGE({img_col}{row_num})'  # Tham chiếu đến ô chứa URL thay vì nhập trực tiếp URL
                            cell_range_status = f"{status_col}{row_num}"
                            image_formula_updates.append({
                                "range": cell_range_status,
                                "values": [[formula]]
                            })
                    
                    # Log thông tin update
                    sheet_update_row = row_count
                    self.results_table.setItem(sheet_update_row, 2, 
                                            QTableWidgetItem(f"Cập nhật {len(update_requests)} URL ảnh và {len(image_formula_updates)} công thức IMAGE..."))
                    QApplication.processEvents()
                    
                    # Upload trực tiếp trong main thread như file logic (không dùng threading)
                    try:
                        # Tạo batch data cho cả URL và IMAGE formula
                        all_batch_data = []

                        # Thêm URL updates
                        if update_requests:
                            all_batch_data.extend(update_requests)

                        # Thêm IMAGE formula updates
                        if image_formula_updates:
                            all_batch_data.extend(image_formula_updates)

                        # Process in chunks để tránh API limits và UI freezing
                        if all_batch_data:
                            total_chunks = (len(all_batch_data) + 49) // 50  # Ceiling division by 50
                            self.results_table.setItem(sheet_update_row, 2,
                                                    QTableWidgetItem(f"Đang cập nhật Google Sheet (0/{total_chunks} chunks)..."))

                            for i in range(0, len(all_batch_data), 50):
                                # Check if processing was stopped during upload
                                if not self.processing:
                                    self.results_table.setItem(sheet_update_row, 2, QTableWidgetItem("Đã dừng cập nhật"))
                                    break

                                chunk = all_batch_data[i:i+50]
                                chunk_num = (i // 50) + 1
                                self.results_table.setItem(sheet_update_row, 2,
                                                        QTableWidgetItem(f"Đang cập nhật Google Sheet ({chunk_num}/{total_chunks} chunks)..."))

                                # Allow UI to update before each chunk
                                QApplication.processEvents()

                                # Upload chunk
                                worksheet.batch_update(chunk, value_input_option="USER_ENTERED")

                                # Allow UI to update after each chunk
                                QApplication.processEvents()

                        print(f"Đã hoàn thành cập nhật {len(update_requests)} URL ảnh và {len(image_formula_updates)} công thức IMAGE")

                    except Exception as upload_error:
                        print(f"Lỗi khi cập nhật Google Sheet: {upload_error}")
                        self.results_table.setItem(sheet_update_row, 2, QTableWidgetItem(f"Lỗi: {str(upload_error)}"))
                        QApplication.processEvents()
                        
                    self.results_table.setItem(sheet_update_row, 2, 
                                            QTableWidgetItem(f"Đã cập nhật {len(update_requests)} URL ảnh và {len(image_formula_updates)} công thức IMAGE"))
                    self.results_table.setItem(sheet_update_row, 3, 
                                            QTableWidgetItem("Hoàn tất"))
                    
                    self.progress_bar.setValue(100)
                    self.update_ui_status("", 100)  # 100%
                    self.show_message("Hoàn thành", "Đã xử lý xong và cập nhật Google Sheet.")
                
                # Xóa kết quả tạm thời vì đã cập nhật đầy đủ
                self.data_manager.clear_all()
                
                # Lưu kết quả vào file logs để backup
                self.save_results_to_file(results)

        except Exception as ex:
            self.update_ui_status(f"Lỗi: {str(ex)}")
            self.show_message("Lỗi", f"Có lỗi xảy ra: {ex}")
        finally:
            self.confirm_button.setEnabled(True)
            self.stop_button.setEnabled(False)
            self.processing = False



    def load_page_with_retry(self, page, url, max_retries=3):
        """Tải trang Shopee với retry, kiểm tra captcha/đăng nhập và bypass Cloudflare (DrissionPage)."""
        fixed_url = self.ensure_https(url)
        retries = 0
        while retries < max_retries:
            try:
                # DrissionPage get method
                page.get(fixed_url, timeout=15)  # Giảm timeout từ 20s xuống 15s
                import time as time_module
                time_module.sleep(1)  # Giảm từ 3s xuống 1s

                # Kiểm tra và bypass Cloudflare nếu có
                if self.is_cloudflare_challenge_dp(page):
                    print("Phát hiện Cloudflare challenge, đang bypass...")
                    if self.bypass_cloudflare_dp(page):
                        print("Bypass Cloudflare thành công!")
                        time_module.sleep(2)  # Chờ trang load sau khi bypass
                    else:
                        print("Bypass Cloudflare thất bại")

                # Kiểm tra các trạng thái khác
                current_url = page.url
                if "buyer/login" in current_url:
                    return "login"
                if "verify/captcha" in current_url:
                    return "captcha"

                # Kiểm tra lại Cloudflare sau khi load
                if self.is_cloudflare_challenge_dp(page):
                    return "cloudflare"

                return "success"
            except Exception as e:
                print(f"Lỗi khi tải {fixed_url}: {e}")
                retries += 1
                wait_time = random.uniform(1, 3)  # Giảm từ 3-7s xuống 1-3s
                print(f"Thử lại sau {wait_time:.1f}s ... (lần {retries}/{max_retries})")
                import time as time_module
                time_module.sleep(wait_time)
        return "failed"

    def is_cloudflare_challenge_dp(self, page):
        """Kiểm tra xem trang có phải là Cloudflare challenge không (DrissionPage)."""
        try:
            title = page.title.lower()
            page_content = page.html.lower()

            # Các dấu hiệu của Cloudflare challenge
            cloudflare_indicators = [
                "just a moment",
                "checking your browser",
                "cloudflare",
                "please wait while we check your browser",
                "ddos protection by cloudflare"
            ]

            for indicator in cloudflare_indicators:
                if indicator in title or indicator in page_content:
                    return True

            # Kiểm tra URL
            if "cf-browser-verification" in page.url or "cloudflare" in page.url:
                return True

            return False
        except Exception as e:
            print(f"Lỗi khi kiểm tra Cloudflare: {e}")
            return False

    def bypass_cloudflare_dp(self, page):
        """Bypass Cloudflare challenge sử dụng CloudflareBypasser (DrissionPage)."""
        try:
            if not CLOUDFLARE_BYPASSER_AVAILABLE:
                print("CloudflareBypasser không khả dụng")
                return False

            # DrissionPage đã tương thích trực tiếp với CloudflareBypasser
            bypasser = CloudflareBypasser(page, max_retries=3, log=True)

            # Thực hiện bypass
            bypasser.bypass()

            # Kiểm tra kết quả
            return bypasser.is_bypassed()

        except Exception as e:
            print(f"Lỗi khi bypass Cloudflare: {e}")
            return False

    def is_cloudflare_challenge(self, page):
        """Kiểm tra xem trang có phải là Cloudflare challenge không."""
        try:
            title = page.title().lower()
            page_content = page.content().lower()

            # Các dấu hiệu của Cloudflare challenge
            cloudflare_indicators = [
                "just a moment",
                "checking your browser",
                "cloudflare",
                "please wait while we check your browser",
                "ddos protection by cloudflare"
            ]

            for indicator in cloudflare_indicators:
                if indicator in title or indicator in page_content:
                    return True

            # Kiểm tra URL
            if "cf-browser-verification" in page.url or "cloudflare" in page.url:
                return True

            return False
        except Exception as e:
            print(f"Lỗi khi kiểm tra Cloudflare: {e}")
            return False

    def bypass_cloudflare(self, page):
        """Bypass Cloudflare challenge sử dụng CloudflareBypasser."""
        try:
            if not CLOUDFLARE_BYPASSER_AVAILABLE:
                print("CloudflareBypasser không khả dụng")
                return False

            # Tạo adapter cho Playwright page
            adapter = PlaywrightCloudflareAdapter(page)
            adapter.update_title()

            # Tạo CloudflareBypasser instance
            bypasser = CloudflareBypasser(adapter, max_retries=3, log=True)

            # Thực hiện bypass
            bypasser.bypass()

            # Kiểm tra kết quả
            adapter.update_title()
            return bypasser.is_bypassed()

        except Exception as e:
            print(f"Lỗi khi bypass Cloudflare: {e}")
            return False

    def ensure_https(self, link):
        """Bổ sung https:// nếu link thiếu giao thức."""
        if not link.startswith("http"):
            return "https://" + link
        return link

    def random_scroll(self, page):
        """Cuộn trang ngẫu nhiên để mô phỏng hành vi người dùng (DrissionPage) - DISABLED."""
        # Function disabled để không làm khó giải captcha
        print("Random scroll đã được tắt để dễ giải captcha")
        return

    def is_valid_image_url(self, src):
        """
        Kiểm tra URL ảnh có hợp lệ không.
        Tuyệt đối không lấy URL từ deo.shopeemobile.com
        """
        if not src or not src.startswith('http'):
            return False

        # Tuyệt đối không lấy URL từ deo.shopeemobile.com
        if 'deo.shopeemobile.com' in src:
            print(f"🚫 Bỏ qua URL deo.shopeemobile.com: {src}")
            return False

        # Bỏ qua ảnh cover
        if "cover" in src:
            return False

        return True

    def find_valid_image_src(self, page):
        """
        Duyệt các ảnh và detect video đúng cách như logic file mẫu (DrissionPage).

        Logic xử lý video:
        - Detect video bằng cách tìm <video> tag
        - Nếu có video và nhiều ảnh → lấy ảnh thứ 2
        - Nếu không có video hoặc chỉ có 1 ảnh → lấy ảnh đầu tiên

        Filter: Tuyệt đối không lấy URL từ deo.shopeemobile.com
        """
        try:
            # 1. Detect video đơn giản và chính xác (với timeout ngắn)
            try:
                video_elements = page.eles('tag:video', timeout=2)  # Timeout 2s thay vì default
                has_video = len(video_elements) > 0
                print(f"🎬 Video detection: {len(video_elements)} video elements found, has_video={has_video}")
            except:
                has_video = False
                print(f"🎬 Video detection: timeout, assuming has_video=False")

            # 2. Tìm tất cả ảnh hợp lệ (tối ưu selector order)
            selectors = [
                'css:img.uXN1L5',  # Selector chính, bỏ .lazyload để nhanh hơn
                'css:img[class*="uXN1L5"]',
                'css:.product-image img',
                'css:.gallery img'
            ]

            valid_images = []

            # Thử các selector chuyên biệt trước (với timeout ngắn)
            for selector in selectors:
                try:
                    imgs = page.eles(selector, timeout=2)  # Timeout 2s cho mỗi selector
                    if imgs:
                        for img in imgs:
                            src = img.attr('src')
                            if self.is_valid_image_url(src):
                                valid_images.append(src)

                        if valid_images:
                            break  # Tìm thấy ảnh với selector này, dừng lại

                except Exception as e:
                    # Không print lỗi để giảm noise
                    continue

            # Fallback: tìm tất cả img tags nếu không tìm thấy với selector chuyên biệt
            if not valid_images:
                try:
                    all_imgs = page.eles('tag:img', timeout=3)  # Timeout 3s cho fallback
                    print(f"Fallback: Tìm thấy {len(all_imgs)} ảnh tổng cộng")

                    # Giới hạn số lượng ảnh check để tăng tốc
                    for img in all_imgs[:20]:  # Chỉ check 20 ảnh đầu tiên
                        src = img.attr('src')
                        if self.is_valid_image_url(src):
                            valid_images.append(src)
                            if len(valid_images) >= 10:  # Đủ 10 ảnh thì dừng
                                break

                except Exception as e:
                    print(f"Lỗi fallback: {e}")

            # 3. Áp dụng logic lấy ảnh dựa trên video detection
            if valid_images:
                print(f"📊 Tổng kết: has_video={has_video}, số ảnh hợp lệ={len(valid_images)}")

                if has_video and len(valid_images) >= 2:
                    # Có video và nhiều ảnh → lấy ảnh thứ 2
                    result = valid_images[1]
                    print(f"✅ Có video, lấy ảnh thứ 2: {result}")
                    return result
                else:
                    # Không có video hoặc chỉ có 1 ảnh → lấy ảnh đầu tiên
                    result = valid_images[0]
                    print(f"✅ Lấy ảnh đầu tiên: {result}")
                    return result
            else:
                print("❌ Không tìm thấy ảnh hợp lệ nào")
                return None

        except Exception as e:
            print(f"Lỗi khi tìm ảnh: {e}")
            return None

    def show_captcha_dialog(self):
        msg = QMessageBox()
        msg.setIcon(QMessageBox.Icon.Warning)
        msg.setWindowTitle("Data Assortment All in One")
        msg.setText(
            "Trang đã chuyển sang đăng nhập hoặc xuất hiện captcha!\n"
            "Vui lòng đăng nhập/giải captcha trên cửa sổ trình duyệt,\n"
            "sau đó bấm OK để tiếp tục."
        )
        msg.setStandardButtons(QMessageBox.StandardButton.Ok)
        msg.exec()

    def show_captcha_dialog_non_blocking(self):
        """Hiển thị dialog captcha với nút OK rõ ràng"""
        msg = QMessageBox(self)  # Set parent để dialog hiển thị đúng
        msg.setIcon(QMessageBox.Icon.Warning)
        msg.setWindowTitle("⚠️ Captcha/Đăng nhập - Data Assortment All in One")
        msg.setText(
            "🔐 Phát hiện trang đăng nhập hoặc captcha!\n\n"
            "📋 Hướng dẫn:\n"
            "1. Chuyển sang cửa sổ trình duyệt\n"
            "2. Đăng nhập hoặc giải captcha\n"
            "3. Quay lại đây và bấm OK để tiếp tục\n\n"
            "⏳ Chương trình sẽ chờ bạn..."
        )
        msg.setStandardButtons(QMessageBox.StandardButton.Ok)

        # Đảm bảo dialog luôn ở trên cùng và có focus
        msg.setWindowFlags(Qt.WindowType.Dialog | Qt.WindowType.WindowStaysOnTopHint)
        msg.setAttribute(Qt.WidgetAttribute.WA_DeleteOnClose)

        # Không tự động set captcha_solved - user phải click auth button
        # msg.finished.connect(lambda result: setattr(self, 'captcha_solved', True))

        # Hiển thị dialog và đưa lên trên cùng
        msg.show()
        msg.raise_()
        msg.activateWindow()

    def show_message(self, title, message):
        """Non-blocking message display để tránh UI freeze"""
        # Sử dụng QTimer để hiển thị message sau khi UI đã update
        QTimer.singleShot(100, lambda: self._show_message_delayed(title, message))

    def _show_message_delayed(self, title, message):
        """Hiển thị message với cơ chế non-blocking"""
        try:
            # Sử dụng show() thay vì exec() để tránh blocking
            msg = QMessageBox(self)
            msg.setIcon(QMessageBox.Icon.Information)
            msg.setWindowTitle(title)
            msg.setText(message)
            msg.setStandardButtons(QMessageBox.StandardButton.Ok)
            msg.setAttribute(Qt.WidgetAttribute.WA_DeleteOnClose)  # Auto cleanup
            msg.show()  # Non-blocking

            # Auto close sau 5 giây nếu user không click
            QTimer.singleShot(5000, msg.close)
        except Exception as e:
            print(f"Error showing message: {e}")
            # Fallback: Hiển thị trong status label
            self.update_ui_status(f"{title}: {message}")

    def col2num(self, col_str):
        """ Convert column letter to number (A=1, B=2, etc) """
        num = 0
        for c in col_str:
            if c in string.ascii_letters:
                num = num * 26 + (ord(c.upper()) - ord('A')) + 1
        return num

    def save_results_to_file(self, results):
        """Lưu kết quả crawl vào file để backup."""
        if not results:
            return

        try:
            # Đảm bảo thư mục tồn tại
            os.makedirs(os.path.dirname(self.log_file_path), exist_ok=True)

            # Lưu kết quả vào file, chế độ append để không mất dữ liệu cũ
            with open(self.log_file_path, "a", encoding="utf-8") as f:
                current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                f.write(f"\n--- Kết quả crawl lúc {current_time} ---\n")

                # Handle both dict and list formats
                if isinstance(results, dict):
                    for row_num, img_url in results.items():
                        f.write(f"Dòng {row_num}: {img_url}\n")
                elif isinstance(results, list):
                    for row_num, img_url in results:
                        f.write(f"Dòng {row_num}: {img_url}\n")
                else:
                    f.write(f"Unknown format: {type(results)}\n")

                f.write(f"Tổng số kết quả: {len(results)}\n")
                f.write("-----------------------------------\n")

            print(f"Đã lưu {len(results)} kết quả vào file: {self.log_file_path}")
        except Exception as e:
            print(f"Không thể lưu kết quả vào file: {str(e)}")

# For standalone testing
if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = ImageScrapingWidget()
    window.show()
    sys.exit(app.exec())
