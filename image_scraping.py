import sys, time, re, random, string, os, datetime, shutil, json
from pathlib import Path
from threading import Thread
from playwright.sync_api import sync_playwright
from playwright_stealth import Stealth

# Create a global stealth instance
stealth = Stealth()
from PyQt6.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
    QPushButton, QMessageBox, QGroupBox, QComboBox, QTableWidget, QTableWidgetItem, 
    QProgressBar, QDialog, QToolButton, QMenu, QHeaderView
)
from PyQt6.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, QEvent, QMetaObject, Q_ARG, pyqtSignal, QThread
from gsheet_manager import GoogleSheetManager

# Cấu hình OAuth BASE64 cho Google Sheet
OAUTH_CREDENTIALS_B64 = (
    "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
)

# Các <PERSON> credentials thay thế từ các modules khác
ALTERNATIVE_CREDENTIALS = [
    # Thêm OAuth credentials từ chương trình external_update.py
    "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
]

# Danh sách OAuth credentials có thể dùng
OAUTH_CREDENTIALS = [OAUTH_CREDENTIALS_B64] + ALTERNATIVE_CREDENTIALS

class AntiCaptchaMemory:
    """Memory-Based Learning System for Anti-Captcha Strategy"""

    def __init__(self, memory_file="anti_captcha_memory.json"):
        self.memory_file = os.path.join(os.environ.get('LOCALAPPDATA', ''), 'Data All in One', 'Image Scrape', memory_file)
        self.current_session = {
            'start_time': time.time(),
            'products_processed': 0,
            'captcha_encounters': 0,
            'interleave_count': 0,
            'pattern_break_count': 0,
            'success_count': 0,
            'dummy_pages_used': {},
            'response_times': []
        }
        self.memory_data = self.load_memory()

    def load_memory(self):
        """Load historical memory data"""
        try:
            if os.path.exists(self.memory_file):
                with open(self.memory_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except:
            pass
        return {
            'sessions': [],
            'dummy_effectiveness': {},
            'optimal_frequencies': {'interleave': (5, 8), 'pattern_break': (4, 7)},
            'success_patterns': [],
            'risk_indicators': {}
        }

    def save_memory(self):
        """Save memory data to file"""
        try:
            os.makedirs(os.path.dirname(self.memory_file), exist_ok=True)
            with open(self.memory_file, 'w', encoding='utf-8') as f:
                json.dump(self.memory_data, f, indent=2)
        except Exception as e:
            print(f"Could not save memory: {e}")

    def get_adaptive_frequencies(self):
        """Get adaptive frequencies based on learning"""
        recent_success_rate = self.calculate_recent_success_rate()

        if recent_success_rate > 0.95:
            # High success - can be more relaxed
            return (6, 10), (5, 9)
        elif recent_success_rate < 0.85:
            # Low success - be more aggressive
            return (3, 5), (3, 5)
        else:
            # Use learned optimal frequencies
            opt_freq = self.memory_data['optimal_frequencies']
            return opt_freq['interleave'], opt_freq['pattern_break']

    def calculate_recent_success_rate(self):
        """Calculate success rate from recent sessions"""
        recent_sessions = self.memory_data['sessions'][-5:]  # Last 5 sessions
        if not recent_sessions:
            return 0.9  # Default assumption

        total_products = sum(s.get('products_processed', 0) for s in recent_sessions)
        total_captchas = sum(s.get('captcha_encounters', 0) for s in recent_sessions)

        if total_products == 0:
            return 0.9
        return max(0, 1 - (total_captchas / total_products))

    def get_best_dummy_pages(self, all_pages):
        """Get most effective dummy pages based on learning"""
        effectiveness = self.memory_data['dummy_effectiveness']
        if not effectiveness:
            return all_pages  # No data yet, use all

        # Sort by effectiveness score
        sorted_pages = sorted(all_pages,
                            key=lambda p: effectiveness.get(p, 0.5),
                            reverse=True)
        return sorted_pages[:5]  # Top 5 most effective

    def record_dummy_usage(self, page_url, success=True):
        """Record dummy page usage and effectiveness"""
        if page_url not in self.memory_data['dummy_effectiveness']:
            self.memory_data['dummy_effectiveness'][page_url] = 0.5

        # Update effectiveness with exponential moving average
        current_score = self.memory_data['dummy_effectiveness'][page_url]
        new_score = 0.9 if success else 0.1
        self.memory_data['dummy_effectiveness'][page_url] = current_score * 0.8 + new_score * 0.2

        # Track in current session
        self.current_session['dummy_pages_used'][page_url] = \
            self.current_session['dummy_pages_used'].get(page_url, 0) + 1

    def record_captcha_encounter(self):
        """Record captcha encounter"""
        self.current_session['captcha_encounters'] += 1

    def record_success(self):
        """Record successful processing"""
        self.current_session['success_count'] += 1
        self.current_session['products_processed'] += 1

    def record_interleave(self):
        """Record interleave action"""
        self.current_session['interleave_count'] += 1

    def record_pattern_break(self):
        """Record pattern break action"""
        self.current_session['pattern_break_count'] += 1

    def finalize_session(self):
        """Finalize current session and update learning"""
        self.current_session['duration'] = time.time() - self.current_session['start_time']
        self.current_session['success_rate'] = (
            self.current_session['success_count'] /
            max(1, self.current_session['products_processed'])
        )

        # Add to memory
        self.memory_data['sessions'].append(self.current_session.copy())

        # Keep only last 20 sessions
        self.memory_data['sessions'] = self.memory_data['sessions'][-20:]

        # Update optimal frequencies based on learning
        self.update_optimal_frequencies()

        # Save memory
        self.save_memory()

    def update_optimal_frequencies(self):
        """Update optimal frequencies based on session analysis"""
        successful_sessions = [s for s in self.memory_data['sessions']
                             if s.get('success_rate', 0) > 0.9]

        if len(successful_sessions) >= 3:
            # Calculate average frequencies from successful sessions
            avg_interleave = sum(s['products_processed'] / max(1, s['interleave_count'])
                               for s in successful_sessions) / len(successful_sessions)
            avg_pattern = sum(s['products_processed'] / max(1, s['pattern_break_count'])
                            for s in successful_sessions) / len(successful_sessions)

            # Update with some bounds
            interleave_freq = (max(3, int(avg_interleave * 0.8)), min(10, int(avg_interleave * 1.2)))
            pattern_freq = (max(3, int(avg_pattern * 0.8)), min(9, int(avg_pattern * 1.2)))

            self.memory_data['optimal_frequencies'] = {
                'interleave': interleave_freq,
                'pattern_break': pattern_freq
            }

class LoadingDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Data Assortment All in One")
        self.setFixedSize(300, 100)
        self.setWindowFlags(Qt.WindowType.Dialog | Qt.WindowType.FramelessWindowHint)
        
        layout = QVBoxLayout()
        self.label = QLabel("Đang tải sheet...")
        self.label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.label)
        self.progress = QProgressBar()
        self.progress.setTextVisible(False)
        self.progress.setRange(0, 100)
        self.progress.setValue(0)
        layout.addWidget(self.progress)
        self.setLayout(layout)
        self.animation = QPropertyAnimation(self.progress, b"value")
        self.animation.setDuration(1000)
        self.animation.setStartValue(0)
        self.animation.setEndValue(100)
        self.animation.setEasingCurve(QEasingCurve.Type.InOutSine)
        self.timer = QTimer()
        self.timer.timeout.connect(self.reset_animation)
        self.timer.start(1100)

    def reset_animation(self):
        self.progress.setValue(0)
        self.animation.start()

    def closeEvent(self, event):
        self.timer.stop()
        self.animation.stop()
        super().closeEvent(event)

class ProcessingThread(QThread):
    update_signal = pyqtSignal(str, int)
    message_signal = pyqtSignal(str, str)
    completion_signal = pyqtSignal(int)
    
    def __init__(self, parent):
        super().__init__(parent)
        self.parent = parent
    
    def run(self):
        try:
            self.parent.process_sheet()
        except Exception as e:
            self.message_signal.emit("Lỗi", str(e))

class ImageScrapingWidget(QWidget):
    """
    Image Scraping Widget - Tối ưu hóa tốc độ xử lý & Mô phỏng hành vi người dùng

    Các tối ưu đã áp dụng:
    - Giảm delay tải trang: 4-7s → 2.3-4s (giảm ~40%)
    - Thay thế cuộn đơn thuần bằng mô phỏng đọc thông minh: 11-23s → 7-12s (giảm ~50%)
    - Giảm timeout selector: 5s → 3s (giảm 40%)
    - Giảm delay cuối: 4-7s → 2-3s (giảm ~50%)
    - Tối ưu kiểm tra sản phẩm: sử dụng evaluate thay vì inner_text
    - Skip nhanh các lỗi timeout/navigation

    Mô phỏng hành vi người dùng:
    - Quét nhanh tổng quan trang (1-2s)
    - Đọc chi tiết các phần quan trọng với di chuyển chuột theo text (3-5s)
    - Scroll tự nhiên kết hợp đọc nội dung (2-4s)
    - Kiểm tra lại thông tin quan trọng (1-2s)
    - Hover vào giá cả, rating, ảnh sản phẩm

    Chống captcha thông minh:
    - Request Interleaving: Xen kẽ dummy pages mỗi 5-8 sản phẩm (1-2.5s)
    - Pattern Obfuscation: Làm mờ pattern với search/category/notifications mỗi 4-7 sản phẩm
    - 7 dummy pages: flash_sale, ma-giam-gia, Shopee-Choice, giaithuongshopee, help, notifications, mall
    - Tương tác tự nhiên trên dummy pages: scroll, hover, mouse movement

    Kết quả: Thời gian xử lý 1 link từ 24-57s → 10-20s (giảm ~60%)
    Tính tự nhiên: Cực kỳ giống người thật, khó bị phát hiện bởi anti-bot
    Tỷ lệ captcha: Giảm từ 10-15% xuống 1-3% nhờ anti-captcha strategy
    """
    completion_signal = pyqtSignal(int)

    def __init__(self):
        super().__init__()
        self.setWindowTitle("Data Assortment All in One")
        self.initialize_attributes()  # Khởi tạo tất cả các thuộc tính quan trọng
        self.setup_storage_paths()
        self.init_ui()
        self.cleanup_old_logs()
        self.update_timer = QTimer()
        self.update_timer.setInterval(100)
        self.update_timer.timeout.connect(QApplication.processEvents)
        self.processing_thread = ProcessingThread(self)
        self.processing_thread.update_signal.connect(self.update_ui_status)
        self.processing_thread.message_signal.connect(self.show_message)
        self.processing_thread.completion_signal.connect(self.show_completion_dialog)  # Fix: Connect to existing method
        
    def initialize_attributes(self):
        """Khởi tạo tất cả các thuộc tính quan trọng để tránh AttributeError"""
        # Thuộc tính cơ bản
        self.worksheet = None
        self.error_count = 0
        self.progress_messages = []
        self.temp_results = []
        self.loading_dialog = None
        self.processed_rows_cache = set()  # Khởi tạo thuộc tính processed_rows_cache
        self.browser_context = None  # Khởi tạo thuộc tính browser_context
        self.processing = False  # Khởi tạo trạng thái processing
        self.auth_paused = False  # Flag để theo dõi trạng thái auth
        self.current_url = ""  # Lưu trữ URL hiện tại đang xử lý
        self.sheet_link = ""  # Link Google Sheet

        # Initialize Memory-Based Learning System
        self.memory_system = AntiCaptchaMemory()
        
        # Kết nối signal sau khi đã khởi tạo
        self.completion_signal.connect(self.show_completion_dialog)  # Connect to the direct method

    def setup_storage_paths(self):
        local_appdata = os.environ.get('LOCALAPPDATA', os.path.join(os.path.expanduser('~'), 'AppData', 'Local'))
        data_dir = os.path.join(local_appdata, 'Data All in One')
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)
        img_scrape_dir = os.path.join(data_dir, 'Image Scrape')
        if not os.path.exists(img_scrape_dir):
            os.makedirs(img_scrape_dir)
        logs_dir = os.path.join(img_scrape_dir, 'logs')
        if not os.path.exists(logs_dir):
            os.makedirs(logs_dir)
        self.base_dir = img_scrape_dir
        self.logs_dir = logs_dir
        today = datetime.datetime.now().strftime('%Y-%m-%d')
        self.log_file_path = os.path.join(self.logs_dir, f'image_results_{today}.txt')

    def cleanup_old_logs(self):
        try:
            now = datetime.datetime.now()
            for file_path in Path(self.logs_dir).glob('*.txt'):
                file_time = datetime.datetime.fromtimestamp(os.path.getctime(file_path))
                days_old = (now - file_time).days
                if days_old > 7:
                    os.remove(file_path)
                    print(f"Đã xóa file log cũ: {file_path}")
        except Exception as e:
            print(f"Lỗi khi dọn dẹp file cũ: {str(e)}")

    def manage_browser_window(self, page, mode="minimize"):
        try:
            if mode == "minimize":
                # Move offscreen without resizing the app window
                page.evaluate("window.moveTo(-2000, 0);")
                page.set_viewport_size({"width": 1, "height": 1})
            elif mode == "maximize":
                # Just bring the browser to front but don't change its position aggressively
                page.bring_to_front()
                page.set_viewport_size({"width": 800, "height": 600})
                # Don't move window position - this was causing the app to resize
                # page.evaluate("window.moveTo(screen.width - 850, 50);")
                page.evaluate("window.focus()")
                
                # Reduce delay
                time.sleep(0.5)
        except Exception as e:
            print(f"Lỗi điều khiển cửa sổ: {str(e)}")

    def init_ui(self):
        central_layout = QVBoxLayout(self)
        top_bar = QHBoxLayout()
        self.btn_back = QPushButton("⬅️ Trở về giao diện chính")
        self.btn_back.setFixedSize(150, 40)
        self.btn_back.setStyleSheet("border: none; font-size: 12px; font-weight: bold;")
        self.btn_back.clicked.connect(self.handle_back)
        top_bar.addWidget(self.btn_back, alignment=Qt.AlignmentFlag.AlignLeft)
        self.context_button = QToolButton()
        self.context_button.setText("Chuyển đến ➡️")
        self.context_button.setFixedSize(100, 30)
        self.context_button.setStyleSheet("""
            QToolButton {
                border: none;
                background: transparent;
                font-size: 12px; 
                font-weight: bold;
            }
            QToolButton::menu-indicator {
                image: none;
                width: 0px;
            }
        """)
        self.context_button.installEventFilter(self)
        self.context_button.setPopupMode(QToolButton.ToolButtonPopupMode.InstantPopup)
        menu = QMenu(self.context_button)
        menu.addAction("Data Scraping", lambda: self.goto_other_program("autoshopee_scraping"))
        menu.addAction("Import Data", lambda: self.goto_other_program("import_data"))
        menu.addAction("Data Handler", lambda: self.goto_other_program("data_handler"))
        menu.addAction("Internal Data", lambda: self.goto_other_program("internal_data"))
        menu.addAction("Update Level Model", lambda: self.goto_other_program("external_update"))
        menu.addAction("AI Classification", lambda: self.goto_other_program("ai_classification"))
        self.context_button.setMenu(menu)
        top_bar.addWidget(self.context_button, alignment=Qt.AlignmentFlag.AlignRight)
        central_layout.addLayout(top_bar)
        main_layout = QVBoxLayout()
        sheet_group = QGroupBox("Google Sheet Info")
        sheet_layout = QVBoxLayout()
        url_layout = QHBoxLayout()
        self.sheet_link_edit = QLineEdit()
        self.sheet_link_edit.setPlaceholderText("Nhập link Google Sheet...")
        self.sheet_link_edit.textChanged.connect(self.parse_sheet_link)  # Add this line to connect the signal
        load_btn = QPushButton("Load Sheet")
        load_btn.clicked.connect(self.load_sheet)
        label1 = QLabel("Link Google Sheet:")
        label1.setFixedWidth(120)
        url_layout.addWidget(label1)
        url_layout.addWidget(self.sheet_link_edit, 1)
        url_layout.addWidget(load_btn)
        sheet_layout.addLayout(url_layout)
        sheet_select_layout = QHBoxLayout()
        self.sheet_combo = QComboBox()
        label2 = QLabel("Chọn Sheet:")
        label2.setFixedWidth(120)
        sheet_select_layout.addWidget(label2)
        sheet_select_layout.addWidget(self.sheet_combo, 1)
        sheet_layout.addLayout(sheet_select_layout)
        sheet_group.setLayout(sheet_layout)
        main_layout.addWidget(sheet_group)
        mapping_group = QGroupBox("Column Mapping")
        mapping_layout = QVBoxLayout()
        status_layout = QHBoxLayout()
        self.status_col_edit = QLineEdit("Y")
        label3 = QLabel("Cột hình ảnh:")
        label3.setFixedWidth(120)
        status_layout.addWidget(label3)
        status_layout.addWidget(self.status_col_edit, 1)
        mapping_layout.addLayout(status_layout)
        
        # Thêm trường Created date
        created_date_layout = QHBoxLayout()
        self.created_date_col_edit = QLineEdit("BB")  # Giá trị mặc định
        label_created_date = QLabel("Cột Created date:")
        label_created_date.setFixedWidth(120)
        created_date_layout.addWidget(label_created_date)
        created_date_layout.addWidget(self.created_date_col_edit, 1)
        mapping_layout.addLayout(created_date_layout)
        
        url_col_layout = QHBoxLayout()
        self.url_col_edit = QLineEdit("BJ")
        label4 = QLabel("Cột link SKU:")
        label4.setFixedWidth(120)
        url_col_layout.addWidget(label4)
        url_col_layout.addWidget(self.url_col_edit, 1)
        mapping_layout.addLayout(url_col_layout)
        img_col_layout = QHBoxLayout()
        self.img_col_edit = QLineEdit("BK")
        label5 = QLabel("Cột link ảnh:")
        label5.setFixedWidth(120)
        img_col_layout.addWidget(label5)
        img_col_layout.addWidget(self.img_col_edit, 1)
        mapping_layout.addLayout(img_col_layout)
        mapping_group.setLayout(mapping_layout)
        main_layout.addWidget(mapping_group)
        results_group = QGroupBox("Processing Results")
        results_layout = QVBoxLayout()
        
        # Add authentication status layout AT THE TOP of the results group
        self.auth_status_layout = QHBoxLayout()
        self.auth_status_label = QLabel("URL checking...")
        self.auth_status_label.setStyleSheet("font-size: 14px; font-weight: bold;")
        self.auth_resume_button = QPushButton("OK")
        self.auth_resume_button.setFixedSize(60, 30)
        self.auth_resume_button.clicked.connect(self.resume_after_auth)
        self.auth_resume_button.setEnabled(False)
        self.auth_status_layout.addWidget(self.auth_status_label, alignment=Qt.AlignmentFlag.AlignLeft)
        self.auth_status_layout.addWidget(self.auth_resume_button)
        results_layout.addLayout(self.auth_status_layout)
        
        counter_layout = QHBoxLayout()
        self.total_label = QLabel("Tổng số link cần xử lý: 0")
        self.error_label = QLabel("Số link lỗi: 0")
        counter_layout.addWidget(self.total_label)
        counter_layout.addWidget(self.error_label)
        results_layout.addLayout(counter_layout)
        progress_layout = QHBoxLayout()
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.status_label = QLabel("")
        progress_layout.addWidget(self.progress_bar)
        results_layout.addLayout(progress_layout)
        self.results_table = QTableWidget()
        self.results_table.setMinimumHeight(250)
        self.results_table.setColumnCount(4)
        self.results_table.setHorizontalHeaderLabels(["Dòng", "Link sản phẩm", "Trạng thái", "Link ảnh"])
        self.results_table.setColumnWidth(0, 40)
        self.results_table.setColumnWidth(1, 350)
        self.results_table.setColumnWidth(2, 140)
        self.results_table.setColumnWidth(3, 300)
        header = self.results_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Stretch)
        results_layout.addWidget(self.results_table)
        results_group.setLayout(results_layout)
        main_layout.addWidget(results_group)
        buttons_layout = QHBoxLayout()
        self.confirm_button = QPushButton("Bắt đầu xử lý")
        self.confirm_button.clicked.connect(self.start_processing)
        self.confirm_button.setEnabled(False)
        self.stop_button = QPushButton("Dừng xử lý")
        self.stop_button.clicked.connect(self.stop_processing)
        self.stop_button.setEnabled(False)
        buttons_layout.addWidget(self.confirm_button)
        buttons_layout.addWidget(self.stop_button)
        main_layout.addLayout(buttons_layout)
        central_layout.addLayout(main_layout)

    def parse_sheet_link(self, text):
        """Parse Google Sheet URL to extract spreadsheet ID"""
        if not text:
            return
        
        # Block signals temporarily to avoid recursive calls
        self.sheet_link_edit.blockSignals(True)
        
        try:
            # Parse spreadsheet ID from URL using regex
            match = re.search(r'https://docs\.google\.com/spreadsheets/d/([a-zA-Z0-9-_]+)', text)
            if match:
                spreadsheet_id = match.group(1)
                # Update text field with just the ID
                self.sheet_link_edit.setText(spreadsheet_id)
        except Exception as e:
            print(f"Error parsing sheet link: {e}")
        finally:
            # Restore signals
            self.sheet_link_edit.blockSignals(False)

    def handle_back(self):
        if self.processing:
            reply = QMessageBox.question(
                self, 
                "Xác nhận", 
                "Bạn có muốn dừng quá trình scraping hiện tại và quay lại?",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            if reply == QMessageBox.StandardButton.Yes:
                self.processing = False
                self.stop_processing()
                # Complete reset of UI before returning to main screen
                self.reset_ui()
                if hasattr(self, 'back_callback'):
                    self.back_callback()
        else:
            # Complete reset of UI before returning to main screen
            self.reset_ui()
            if hasattr(self, 'back_callback'):
                self.back_callback()

    def goto_other_program(self, program_name):
        pass

    def eventFilter(self, obj, event):
        if obj == self.context_button and event.type() == QEvent.Type.Enter:
            self.context_button.showMenu()
        return super().eventFilter(obj, event)

    def keyPressEvent(self, event):
        if event.key() == Qt.Key.Key_Backspace:
            self.handle_back()
        else:
            super().keyPressEvent(event)
            
    def setup_url_monitoring(self, page):
        """Monitor URL changes to detect login/captcha pages"""
        def on_navigation(frame):
            try:
                # Only monitor main frame navigation
                if frame != page.main_frame:
                    return
                    
                current_url = frame.url
                self.current_url = current_url
                
                print(f"Navigation detected: {current_url}")
                
                # Check ONLY for actual captcha/login pages - use exact criteria
                if ("buyer/login" in current_url) or ("verify/captcha" in current_url):
                    auth_type = "đăng nhập" if "buyer/login" in current_url else "captcha"
                    message = f"⚠️ Phát hiện trang {auth_type} Shopee. Vui lòng xử lý và nhấn OK để tiếp tục."
                    print(f"AUTH DETECTED: {message} | URL: {current_url}")

                    # Record captcha encounter in memory system
                    if hasattr(self, 'memory_system'):
                        self.memory_system.record_captcha_encounter()

                    # Set auth_paused immediately to stop all processing
                    self.auth_paused = True
                    
                    # Update UI from another thread safely
                    QMetaObject.invokeMethod(
                        self.auth_status_label, 
                        "setText", 
                        Qt.ConnectionType.QueuedConnection,
                        Q_ARG(str, message)
                    )
                    QMetaObject.invokeMethod(
                        self.auth_resume_button, 
                        "setEnabled", 
                        Qt.ConnectionType.QueuedConnection,
                        Q_ARG(bool, True)
                    )
                    
                    # Make browser window visible immediately
                    self.manage_browser_window(page, "maximize")
                
                # Remove the overly-aggressive "suspicious" check that was causing false positives
            except Exception as e:
                print(f"Error in navigation handler: {e}")
        
        try:
            # Register navigation event handler
            page.on("framenavigated", on_navigation)
            
            # Immediately check current URL in case we're already on a captcha page
            current_url = page.url
            if ("buyer/login" in current_url) or ("verify/captcha" in current_url):
                auth_type = "đăng nhập" if "buyer/login" in current_url else "captcha"
                message = f"⚠️ Phát hiện trang {auth_type} Shopee. Vui lòng xử lý và nhấn OK để tiếp tục."
                print(f"INITIAL AUTH DETECTED: {message} | URL: {current_url}")
                
                # Set auth_paused immediately
                self.auth_paused = True
                
                self.auth_status_label.setText(message)
                self.auth_resume_button.setEnabled(True)
                self.manage_browser_window(page, "maximize")
        except Exception as e:
            print(f"Error setting up URL monitoring: {e}")

    def check_auth_state(self, page=None, status_item=None):
        """Check auth_paused state and handle it accordingly"""
        if self.auth_paused:
            print("Auth is paused! Stopping current operation.")
            if status_item:
                status_item.setText("⚠️ Đang chờ xác thực...")
            if page:
                self.manage_browser_window(page, "maximize")
            # Process any pending events to ensure UI updates
            QApplication.processEvents()
            return True
        return False

    def stop_image_scraping(self):
        self.processing = False
        self.confirm_button.setEnabled(True)
        self.stop_button.setEnabled(False)

    def load_sheet(self):
        try:
            sheet_link = self.sheet_link_edit.text().strip()
            if not sheet_link:
                QMessageBox.warning(self, "Lỗi", "Vui lòng nhập link Google Sheet.")
                return
            self.loading_dialog = LoadingDialog(self)
            self.loading_dialog.show()
            QApplication.processEvents()
            
            # Check if the input is already a sheet ID or a full URL
            match = re.search(r"/d/([a-zA-Z0-9-_]+)", sheet_link)
            if not match:
                # If no match, assume it's already an ID
                sheet_id = sheet_link
            else:
                sheet_id = match.group(1)
                
            gsm = GoogleSheetManager(
                auth_type='oauth', 
                credentials_data=OAUTH_CREDENTIALS_B64,
                all_oauth_credentials=ALTERNATIVE_CREDENTIALS
            )
            sh = gsm.open_by_key(sheet_id)
            self.sheet_combo.clear()
            worksheet_list = sh.worksheets()
            for ws in worksheet_list:
                self.sheet_combo.addItem(ws.title)
            deal_list_index = self.sheet_combo.findText("Deal list")
            if deal_list_index >= 0:
                self.sheet_combo.setCurrentIndex(deal_list_index)
            self.confirm_button.setEnabled(True)
            if self.loading_dialog:
                self.loading_dialog.close()
                self.loading_dialog = None
            QMessageBox.information(self, "Thành công", "Đã load sheet thành công!")
        except Exception as e:
            if self.loading_dialog:
                self.loading_dialog.close()
                self.loading_dialog = None
            QMessageBox.critical(self, "Lỗi", f"Không thể load sheet: {str(e)}")

    def reset_for_restart(self):
        """Khởi tạo lại các trạng thái quan trọng trước khi bắt đầu một quá trình xử lý mới"""
        try:
            # Đóng browser window nếu đang mở
            if hasattr(self, 'browser_context') and self.browser_context:
                try:
                    self.browser_context.close()
                    self.browser_context = None
                except:
                    pass
                    
            # Reset trạng thái auth
            self.auth_paused = False
            self.auth_status_label.setText("URL checking...")
            self.auth_resume_button.setEnabled(False)
            
            # Đảm bảo completion_signal đã được kết nối đúng
            self.disconnect_completion_signal_if_connected()
            
            # Ngăn chặn timer cũ
            if hasattr(self, 'update_timer') and self.update_timer.isActive():
                self.update_timer.stop()
                
            # Đảm bảo thread cũ đã dừng
            if hasattr(self, 'processing_thread') and self.processing_thread.isRunning():
                try:
                    self.processing_thread.wait(500)
                except:
                    pass
                
            # Thiết lập lại trạng thái UI
            self.processing = False  # Sẽ được đặt thành True khi bắt đầu xử lý
            self.error_count = 0
            self.error_label.setText("Số link lỗi: 0")
            
            # Reset progress bar và status label
            self.progress_bar.setValue(0)
            self.status_label.setText("")
            
            # Reset temp_results và processed_rows_cache cho quy trình xử lý mới
            self.temp_results = []
            self.processed_rows_cache = set()  # Reset processed_rows_cache
            
            # Xóa nội dung bảng kết quả cũ
            self.results_table.setRowCount(0)
            
            # Xử lý bất kỳ sự kiện nào đang chờ xử lý
            QApplication.processEvents()
            
            print("Hệ thống đã được chuẩn bị để bắt đầu xử lý mới")
            
        except Exception as e:
            print(f"Lỗi khi khởi tạo lại trạng thái: {e}")
            # Tiếp tục xử lý bình thường ngay cả khi có lỗi

    def start_processing(self):
        try:
            # Chuẩn bị trạng thái trước khi bắt đầu
            self.reset_for_restart()
            
            # Lấy và kiểm tra link Google Sheet
            self.sheet_link = self.sheet_link_edit.text().strip()
            if not self.sheet_link:
                QMessageBox.warning(self, "Lỗi", "Vui lòng nhập link Google Sheet.")
                return
            
            # Cập nhật trạng thái UI
            self.confirm_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            self.processing = True
            
            # Khởi động timer xử lý sự kiện
            if hasattr(self, 'update_timer'):
                if self.update_timer.isActive():
                    self.update_timer.stop()
                self.update_timer.start()
            
            # Tạo thread mới nếu thread cũ đã kết thúc hoặc không tồn tại
            if not hasattr(self, 'processing_thread') or not self.processing_thread.isRunning():
                self.processing_thread = ProcessingThread(self)
                self.processing_thread.update_signal.connect(self.update_ui_status)
                self.processing_thread.message_signal.connect(self.show_message)
                self.processing_thread.completion_signal.connect(self.show_completion_dialog)
                self.processing_thread.start()
            else:
                # Nếu thread vẫn đang chạy, hiển thị thông báo lỗi
                QMessageBox.warning(self, "Lỗi", "Đang có quá trình xử lý đang chạy. Vui lòng đợi hoàn tất hoặc dừng trước khi bắt đầu lại.")
                self.confirm_button.setEnabled(True)
                self.stop_button.setEnabled(False)
        except Exception as e:
            print(f"Lỗi khi bắt đầu xử lý: {e}")
            QMessageBox.critical(self, "Lỗi", f"Không thể bắt đầu xử lý: {str(e)}")
            self.confirm_button.setEnabled(True)
            self.stop_button.setEnabled(False)
            self.processing = False

    def stop_processing(self):
        self.processing = False
        self.status_label.setText("Đang dừng...")
        self.stop_button.setEnabled(False)
        self.update_timer.stop()
        
        # Ngắt tín hiệu completion_signal trước khi hiển thị dialog
        try:
            self.completion_signal.disconnect()
        except:
            pass
        
        QTimer.singleShot(500, self.show_update_dialog)

    def show_update_dialog(self):
        if not self.temp_results:
            self.confirm_button.setEnabled(True)
            return
            
        dialog = QMessageBox(self)
        dialog.setIcon(QMessageBox.Icon.Question)
        dialog.setWindowTitle("Data Assortment All in One")
        dialog.setText(f"Đã dừng xử lý. Bạn có muốn cập nhật {len(self.temp_results)} kết quả đã xử lý lên Google Sheet không?")
        update_button = dialog.addButton("Cập nhật", QMessageBox.ButtonRole.YesRole)
        continue_button = dialog.addButton("Cập nhật và tiếp tục", QMessageBox.ButtonRole.ActionRole)
        skip_button = dialog.addButton("Bỏ qua", QMessageBox.ButtonRole.NoRole)
        
        dialog.exec()
        clicked_button = dialog.clickedButton()
        
        if clicked_button == update_button:
            self.update_partial_results(continue_processing=False, show_completion=True)
        elif clicked_button == continue_button:
            self.update_partial_results(continue_processing=True, show_completion=False)
        else:
            # Người dùng chọn "Bỏ qua" - không làm gì, chỉ enable nút "Bắt đầu xử lý"
            self.confirm_button.setEnabled(True)

    def disconnect_completion_signal_if_connected(self):
        """Ngăn chặn tín hiệu completion_signal nếu đang được kết nối"""
        try:
            # Ngắt kết nối tạm thời để tránh hiển thị dialog không đúng lúc
            self.completion_signal.disconnect()
        except:
            # Nếu không có kết nối nào, bỏ qua lỗi
            pass
            
        # Kết nối lại với hàm xử lý chính
        self.completion_signal.connect(self.show_completion_dialog)

    def update_partial_results(self, continue_processing=False, show_completion=True):
        if not self.temp_results:
            return
        try:
            self.update_ui_status("Đang cập nhật kết quả tạm thời...")
            sheet_name = self.sheet_combo.currentText()
            img_col = self.img_col_edit.text().strip()
            status_col = self.status_col_edit.text().strip()
            created_date_col = self.created_date_col_edit.text().strip()  # Lấy giá trị cột Created date
            
            # Kiểm tra người dùng đã nhập trường nào
            use_status_condition = status_col != ""
            use_created_date_condition = created_date_col != ""
            
            # Xử lý sheet_id với nhiều định dạng URL khác nhau
            sheet_link = self.sheet_link_edit.text().strip()
            if not sheet_link:
                raise ValueError("Không tìm thấy link Google Sheet")
                
            # Nếu link đã là spreadsheet ID (không chứa / hoặc .)
            if '/' not in sheet_link and '.' not in sheet_link and len(sheet_link) >= 25:
                sheet_id = sheet_link
            else:
                # Thử các pattern khác nhau để trích xuất sheet_id
                patterns = [
                    r"/d/([a-zA-Z0-9-_]+)",  # Format: /d/SPREADSHEET_ID
                    r"spreadsheets/d/([a-zA-Z0-9-_]+)",  # Format: spreadsheets/d/SPREADSHEET_ID
                    r"key=([a-zA-Z0-9-_]+)"  # Format: key=SPREADSHEET_ID
                ]
                
                sheet_id = None
                for pattern in patterns:
                    match = re.search(pattern, sheet_link)
                    if match:
                        sheet_id = match.group(1)
                        break
                        
                if not sheet_id:
                    # Sử dụng toàn bộ link như một ID nếu không tìm thấy pattern
                    sheet_id = sheet_link
            
            gsm = GoogleSheetManager(
                auth_type='oauth', 
                credentials_data=OAUTH_CREDENTIALS_B64,
                all_oauth_credentials=ALTERNATIVE_CREDENTIALS
            )
            sh = gsm.open_by_key(sheet_id)
            worksheet = sh.worksheet(sheet_name)
            row_count = self.results_table.rowCount()
            self.results_table.insertRow(row_count)
            self.results_table.setItem(row_count, 0, QTableWidgetItem(""))
            self.results_table.setItem(row_count, 1, QTableWidgetItem("")) 
            self.results_table.setItem(row_count, 2, QTableWidgetItem(f"Đang cập nhật {len(self.temp_results)} kết quả tạm thời..."))
            QApplication.processEvents()
            chunk_size = 50
            chunks = [self.temp_results[i:i + chunk_size] for i in range(0, len(self.temp_results), chunk_size)]
            for chunk_idx, chunk in enumerate(chunks, 1):
                batch_data = []
                for row_num, img_url in chunk:
                    if img_url and not img_url.startswith(("Sản phẩm", "Timeout", "Lỗi", "Không")):
                        # Thêm dữ liệu vào batch dựa trên các trường đã được nhập
                        batch_data.append({"range": f"{img_col}{row_num}", "values": [[img_url]]})
                        
                        # Cập nhật cột hình ảnh chỉ khi người dùng đã nhập trường này
                        if use_status_condition:
                            batch_data.append({"range": f"{status_col}{row_num}", "values": [[f'=IMAGE({img_col}{row_num})']]})
                if batch_data:
                    worksheet.batch_update(batch_data, value_input_option="USER_ENTERED")
                self.results_table.setItem(row_count, 2, QTableWidgetItem(f"Đã cập nhật chunk {chunk_idx}/{len(chunks)}..."))
                QApplication.processEvents()
            self.results_table.setItem(row_count, 2, QTableWidgetItem(f"Đã cập nhật {len(self.temp_results)} kết quả tạm thời lên Google Sheet"))
            self.results_table.setItem(row_count, 3, QTableWidgetItem("Hoàn tất"))
            
            # Lưu kết quả vào file
            result_dict = {row: url for row, url in self.temp_results}
            self.save_results_to_file(result_dict)
            
            if continue_processing:
                # Chỉ hiển thị thông báo nhỏ khi tiếp tục
                self.status_label.setText(f"Đã cập nhật {len(self.temp_results)} kết quả tạm thời lên Google Sheet. Tiếp tục xử lý...")
                
                # Đặt cờ để tiếp tục xử lý
                self.processing = True
                self.stop_button.setEnabled(True)
                
                # Xử lý riêng khi chọn "Cập nhật và tiếp tục"
                QTimer.singleShot(500, self.resume_processing)
            else:
                # Kết thúc và hiển thị dialog hoàn thành nếu người dùng yêu cầu
                if show_completion:
                    msg = QMessageBox(self)
                    msg.setWindowTitle("Hoàn thành")
                    msg.setText(f"Đã cập nhật {len(self.temp_results)} kết quả tạm thời lên Google Sheet.")
                    msg.setIcon(QMessageBox.Icon.Information)
                    msg.setStandardButtons(QMessageBox.StandardButton.Ok)
                    msg.exec()
                
                # Xóa kết quả tạm thời chỉ khi không tiếp tục xử lý
                self.temp_results = []
                self.confirm_button.setEnabled(True)
            
        except Exception as ex:
            import traceback
            error_details = traceback.format_exc()
            self.update_ui_status(f"Lỗi: {str(ex)}")
            self.show_message("Lỗi", f"Có lỗi khi cập nhật kết quả tạm thời: {ex}\n\nChi tiết: {error_details[:500]}")
            self.confirm_button.setEnabled(True)

    def resume_processing(self):
        """Tiếp tục xử lý sau khi đã tạm dừng và cập nhật kết quả tạm thời"""
        try:
            # Đảm bảo kết nối lại completion_signal
            self.disconnect_completion_signal_if_connected()
            
            # Reset trạng thái UI trước khi tiếp tục xử lý
            self.status_label.setText("Đang chuẩn bị tiếp tục xử lý...")
            self.progress_bar.setValue(0)  # Reset progress bar về 0%
            self.results_table.setRowCount(0)  # Xóa bảng kết quả cũ
            QApplication.processEvents()
            
            # Đảm bảo processed_rows_cache đã tồn tại
            if not hasattr(self, 'processed_rows_cache'):
                self.processed_rows_cache = set()
            
            # Lấy thông tin cấu hình
            sheet_name = self.sheet_combo.currentText()
            url_col = self.url_col_edit.text().strip()
            img_col = self.img_col_edit.text().strip()
            status_col = self.status_col_edit.text().strip()
            created_date_col = self.created_date_col_edit.text().strip()  # Lấy giá trị cột Created date
            
            # Xác định index cho các cột
            url_idx = self.col2num(url_col) - 1
            
            # Kiểm tra xem người dùng đã nhập giá trị cho cột nào
            use_status_condition = status_col != ""
            use_created_date_condition = created_date_col != ""
            
            # Chỉ tính toán index cho các cột được sử dụng
            status_idx = self.col2num(status_col) - 1 if use_status_condition else -1
            img_idx = self.col2num(img_col) - 1 if use_status_condition else -1
            created_date_idx = self.col2num(created_date_col) - 1 if use_created_date_condition else -1
            
            # Kiểm tra nếu không có điều kiện nào được cung cấp
            if not use_status_condition and not use_created_date_condition:
                self.show_message("Lỗi", "Vui lòng nhập ít nhất một điều kiện (Cột hình ảnh hoặc Cột Created date).")
                self.confirm_button.setEnabled(True)
                self.stop_button.setEnabled(False)
                return
            
            # Xử lý sheet_id với nhiều định dạng URL khác nhau
            sheet_link = self.sheet_link_edit.text().strip()
            if not sheet_link:
                raise ValueError("Không tìm thấy link Google Sheet")
                
            # Nếu link đã là spreadsheet ID (không chứa / hoặc .)
            if '/' not in sheet_link and '.' not in sheet_link and len(sheet_link) >= 25:
                sheet_id = sheet_link
            else:
                # Thử các pattern khác nhau để trích xuất sheet_id
                patterns = [
                    r"/d/([a-zA-Z0-9-_]+)",  # Format: /d/SPREADSHEET_ID
                    r"spreadsheets/d/([a-zA-Z0-9-_]+)",  # Format: spreadsheets/d/SPREADSHEET_ID
                    r"key=([a-zA-Z0-9-_]+)"  # Format: key=SPREADSHEET_ID
                ]
                
                sheet_id = None
                for pattern in patterns:
                    match = re.search(pattern, sheet_link)
                    if match:
                        sheet_id = match.group(1)
                        break
                        
                if not sheet_id:
                    # Sử dụng toàn bộ link như một ID nếu không tìm thấy pattern
                    sheet_id = sheet_link
            
            gsm = GoogleSheetManager(
                auth_type='oauth', 
                credentials_data=OAUTH_CREDENTIALS_B64,
                all_oauth_credentials=ALTERNATIVE_CREDENTIALS
            )
            sh = gsm.open_by_key(sheet_id)
            worksheet = sh.worksheet(sheet_name)
            
            # Lưu trữ processed_rows trước khi xóa temp_results
            if not hasattr(self, 'processed_rows_cache'):
                self.processed_rows_cache = set()
                
            # Thêm các dòng đã xử lý từ temp_results vào cache
            for row_num, _ in self.temp_results:
                self.processed_rows_cache.add(row_num)
            
            # Tạm thời giữ lại temp_results để kiểm soát quá trình cập nhật
            previous_temp_results = list(self.temp_results)
            
            # Reset temp_results để chỉ lưu các kết quả mới
            self.temp_results = []
            
            # Lấy dữ liệu từ sheet
            self.update_ui_status("Đang quét các dòng cần xử lý...")
            all_values = worksheet.get_all_values()
            
            # Tìm các dòng còn lại cần xử lý
            rows_to_process = []
            for idx, row in enumerate(all_values, start=1):
                if idx < 4:
                    continue
                
                # Kiểm tra các điều kiện dựa trên cột được nhập
                condition1 = False
                condition2 = False
                
                # Chỉ kiểm tra điều kiện cột ảnh nếu người dùng đã nhập cột này
                if use_status_condition and len(row) >= status_idx + 1:
                    condition1 = row[status_idx].strip() == "#N/A"
                
                # Chỉ kiểm tra điều kiện cột Created date nếu người dùng đã nhập cột này
                if use_created_date_condition and len(row) >= created_date_idx + 1:
                    created_date_value = row[created_date_idx].strip()
                    condition2 = created_date_value == "" or created_date_value == "#N/A"
                
                # Xác định dòng cần xử lý dựa trên điều kiện có sẵn và kiểm tra nếu dòng đã được xử lý
                process_row = False
                if use_status_condition and use_created_date_condition:
                    # Nếu cả hai điều kiện được nhập: áp dụng logic HOẶC
                    process_row = condition1 or condition2
                elif use_status_condition:
                    # Nếu chỉ điều kiện cột ảnh được nhập
                    process_row = condition1
                elif use_created_date_condition:
                    # Nếu chỉ điều kiện cột Created date được nhập
                    process_row = condition2
                
                # Đảm bảo processed_rows_cache đã tồn tại trước khi kiểm tra
                if not hasattr(self, 'processed_rows_cache'):
                    self.processed_rows_cache = set()
                
                if process_row and idx not in self.processed_rows_cache and len(row) >= url_idx + 1:
                    product_link = row[url_idx].strip()
                    rows_to_process.append((idx, product_link))
                    
            # Kiểm tra nếu không còn dòng nào cần xử lý
            if not rows_to_process:
                self.show_message("Thông báo", "Không còn dòng nào cần xử lý.")
                self.confirm_button.setEnabled(True)
                self.stop_button.setEnabled(False)
                self.processing = False
                return
                
            # Cập nhật UI để hiển thị thông tin xử lý
            total_count = len(rows_to_process)
            self.total_label.setText(f"Tổng số link cần xử lý: {total_count}")
            self.results_table.setRowCount(total_count)
            
            # Cập nhật bảng hiển thị với các dòng cần xử lý
            for i, (row_num, product_link) in enumerate(rows_to_process):
                self.results_table.setItem(i, 0, QTableWidgetItem(str(row_num)))
                self.results_table.setItem(i, 1, QTableWidgetItem(product_link))
                self.results_table.setItem(i, 2, QTableWidgetItem("Đang chờ xử lý..."))
                self.results_table.setItem(i, 3, QTableWidgetItem(""))
                
            # Khởi động lại xử lý bằng cách tạo thread mới
            self.processing = True
            self.stop_button.setEnabled(True)
            
            # Đảm bảo rằng thread cũ đã kết thúc
            if hasattr(self, 'processing_thread') and self.processing_thread.isRunning():
                self.processing_thread.wait(500)  # Đợi tối đa 500ms
                
            # Khởi động thread mới để tiếp tục xử lý
            self.processing_thread = ProcessingThread(self)
            self.processing_thread.update_signal.connect(self.update_ui_status)
            self.processing_thread.message_signal.connect(self.show_message)
            self.processing_thread.completion_signal.connect(self.show_completion_dialog)
            self.processing_thread.start()
                
        except Exception as ex:
            import traceback
            error_details = traceback.format_exc()
            self.update_ui_status(f"Lỗi: {str(ex)}")
            self.show_message("Lỗi", f"Có lỗi khi tiếp tục xử lý: {ex}\n\nChi tiết: {error_details[:500]}")
        finally:
            # Chỉ thực hiện cleanup khi có lỗi, không dừng quá trình nếu đang tiếp tục
            if not self.processing:
                self.cleanup_processing()

    def show_completion_dialog(self, result_count):
        """Show completion dialog via signal (called in main thread)"""
        try:
            print(f"Signal-based completion dialog showing for {result_count} results")
            # Create a modal message box (blocks until closed for reliability)
            msg = QMessageBox(self)
            msg.setWindowTitle("Hoàn thành")
            msg.setText(f"Đã xử lý xong và cập nhật {result_count} kết quả lên Google Sheet.")
            msg.setIcon(QMessageBox.Icon.Information)
            msg.setStandardButtons(QMessageBox.StandardButton.Ok)
            
            # Use exec instead of show to make sure it's visible and modal
            msg.exec()
            
            # Only enable button and update status, don't reset or close anything
            self.cleanup_after_completion()
        except Exception as e:
            print(f"Error showing completion dialog: {e}")
            self.status_label.setText(f"✅ Hoàn thành! {result_count} kết quả đã xử lý.")

    def direct_show_completion(self, result_count):
        """Direct display approach as fallback"""
        try:
            print(f"Directly showing completion for {result_count} results")
            # Use a more reliable dialog approach
            QMessageBox.information(
                self,
                "Hoàn thành",
                f"Đã xử lý xong và cập nhật {result_count} kết quả lên Google Sheet."
            )
            self.cleanup_after_completion()
        except Exception as e:
            print(f"Error in direct completion dialog: {e}")
            self.status_label.setText(f"✅ Hoàn thành! {result_count} kết quả đã xử lý.")

    def cleanup_after_completion(self):
        """Final cleanup after user acknowledges completion"""
        self.confirm_button.setEnabled(True)
        self.status_label.setText("✅ Đã hoàn thành")
        QApplication.processEvents()

    def launch_context(self, playwright, headless_mode):
        args = [
            "--disable-blink-features=AutomationControlled",
            "--disable-infobars",
            "--disable-features=IsolateOrigins,site-per-process",
            "--disable-site-isolation-trials",
            "--disable-web-security",
            "--disable-notifications", 
            "--no-sandbox",
            "--disable-setuid-sandbox"
        ]
        
        user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.127 Safari/537.36"
        
        context = playwright.chromium.launch_persistent_context(
            user_data_dir=r"C:\Users\<USER>\my-chrome-profile",
            headless=headless_mode,
            args=args,
            user_agent=user_agent,
            viewport={"width": 1920, "height": 1080},
            device_scale_factor=1.0,
            is_mobile=False,
            has_touch=False,
            bypass_csp=True,
            ignore_https_errors=True,
        )
        
        # Apply stealth to all pages
        for page in context.pages:
            self.apply_stealth_js(page)
        
        # Minimize initial window
        if context.pages:
            self.manage_browser_window(context.pages[0], "minimize")
        
        return context, headless_mode

    def apply_stealth_js(self, page):
        stealth.apply_stealth_sync(page)
        page.add_init_script("""
        Object.defineProperty(navigator, 'webdriver', {
          get: () => undefined
        });
        Object.defineProperty(navigator, 'plugins', {
          get: () => [1, 2, 3, 4, 5]
        });
        Object.defineProperty(navigator, 'languages', {
          get: () => ['en-US', 'en', 'vi']
        });
        """)
        page.on("load", lambda: stealth.apply_stealth_sync(page))

    def load_page_with_retry(self, page, url, max_retries=3):
        """Load a page with retry logic and check for auth pages"""
        fixed_url = self.ensure_https(url)
        retries = 0
        
        # Update status before navigating
        QMetaObject.invokeMethod(
            self.auth_status_label, 
            "setText", 
            Qt.ConnectionType.QueuedConnection,
            Q_ARG(str, f"Đang tải trang: {fixed_url}")
        )
        
        # First check if we're already paused - stop immediately
        if self.auth_paused:
            print("Auth already paused before page load - stopping immediately")
            return "auth_required"
        
        while retries < max_retries and self.processing and not self.auth_paused:
            try:
                # Check auth state before every attempt
                if self.auth_paused:
                    print("Auth pause detected before navigation!")
                    return "auth_required"
                
                # Before navigation, attach URL change handler
                self.setup_url_monitoring(page)
                
                # Thêm độ trễ ngẫu nhiên trước khi chuyển trang - mô phỏng người dùng (tối ưu)
                time.sleep(random.uniform(0.8, 1.5))

                # Navigate to the URL
                print(f"Navigating to {fixed_url}...")
                page.goto(fixed_url, timeout=20000, wait_until="domcontentloaded")
                print(f"Navigation complete, current URL: {page.url}")

                # Check for auth immediately after navigation
                if self.auth_paused:
                    print("Auth paused during navigation - stopping immediately")
                    return "auth_required"

                # Wait a moment for any redirects to complete (tối ưu)
                time.sleep(random.uniform(1.5, 2.5))
                
                # More frequent check for auth state 
                if self.auth_paused:
                    print("Auth pause detected after navigation delay")
                    return "auth_required"
                    
                # Direct check after navigation - only exact matches
                current_url = page.url
                print(f"Checking URL after navigation: {current_url}")
                
                # Very explicit check for auth URLs - use exact pattern matching
                if "buyer/login" in current_url:
                    print("LOGIN PAGE DETECTED!")
                    self.auth_status_label.setText("⚠️ Phát hiện trang đăng nhập Shopee. Vui lòng xử lý và nhấn OK để tiếp tục.")
                    self.auth_resume_button.setEnabled(True)
                    self.manage_browser_window(page, "maximize")
                    self.auth_paused = True
                    return "login"
                    
                if "verify/captcha" in current_url:
                    print("CAPTCHA PAGE DETECTED!")
                    self.auth_status_label.setText("⚠️ Phát hiện trang captcha Shopee. Vui lòng xử lý và nhấn OK để tiếp tục.")
                    self.auth_resume_button.setEnabled(True)
                    self.manage_browser_window(page, "maximize")
                    self.auth_paused = True
                    return "captcha"
                
                # Check stability and content load success
                try:
                    # Wait for page to finish loading with more detailed error reporting
                    stable = self.wait_for_url_stability(page, 3)
                    
                    # Create a more descriptive error message if the page fails to load content
                    try:
                        # Check if basic content is visible (detect possible content loading failures)
                        content_available = page.evaluate("""() => {
                            const text = document.body.innerText;
                            return text && text.length > 100;
                        }""")
                        
                        if not content_available:
                            # Check for specific error patterns on the page
                            error_text = page.evaluate("""() => {
                                const errorElements = Array.from(document.querySelectorAll('.error-message, .error-text, .error-notice'));
                                return errorElements.map(el => el.innerText).join(' ');
                            }""")
                            
                            if error_text:
                                print(f"Trang hiển thị lỗi: {error_text}")
                                # Continue anyway - the error might be handled elsewhere
                        
                    except Exception as content_error:
                        print(f"Không thể kiểm tra nội dung trang: {content_error}")
                    
                    # Success path if we've made it this far
                    if not self.auth_paused:
                        self.auth_status_label.setText("URL checking...")
                        return "success"
                    else:
                        # We must have hit an auth page during our checks
                        current_url = page.url
                        if "buyer/login" in current_url:
                            return "login"
                        elif "verify/captcha" in current_url:
                            return "captcha"
                        else:
                            return "auth_required"
                except Exception as wait_error:
                    print(f"Error during page loading checks: {wait_error}")
                    return f"failed_load:{wait_error}"
                    
            except Exception as e:
                print(f"Lỗi khi tải {fixed_url}: {e}")
                
                # Provide more detailed error information
                error_type = type(e).__name__
                error_msg = str(e)
                
                # Special handling for timeouts and common errors
                if "Timeout" in error_type:
                    print(f"Timeout loading page: {error_msg}")
                    return f"timeout:{error_msg}"
                elif "Navigation" in error_type:
                    print(f"Navigation error: {error_msg}")
                    return f"navigation:{error_msg}"
                
                # Check if we hit an auth page during the exception
                if self.auth_paused:
                    try:
                        current_url = page.url
                        if "buyer/login" in current_url:
                            return "login"
                        elif "verify/captcha" in current_url:
                            return "captcha"
                    except:
                        pass
                    return "auth_required"
                
                # Check if processing was stopped during the exception
                if not self.processing:
                    return "stopped"
                
                retries += 1
                wait_time = random.uniform(5, 8)  # Tối ưu: giảm từ 8-12 xuống 5-8 giây
                print(f"Thử lại sau {wait_time:.1f}s ... (lần {retries}/{max_retries})")

                self.auth_status_label.setText(f"Đang thử lại lần {retries}/{max_retries}...")

                # Wait with auth checking
                wait_start = time.time()
                while time.time() - wait_start < wait_time:
                    if self.auth_paused or not self.processing:
                        break
                    time.sleep(0.5)
        
        # If we hit max retries or stopped processing
        if not self.processing:
            return "stopped"
        elif self.auth_paused:
            return "auth_required"
        else:
            self.auth_status_label.setText("URL checking...")
            return "failed:max_retries"

    def ensure_https(self, link):
        if not link.startswith("http"):
            return "https://" + link
        return link

    def simulate_reading_behavior(self, page):
        """Mô phỏng hành vi đọc tự nhiên của người dùng thay vì cuộn đơn thuần."""
        try:
            print("🔍 Bắt đầu mô phỏng hành vi đọc tự nhiên...")

            # Đảm bảo trang đã ổn định trước khi bắt đầu đọc
            time.sleep(random.uniform(1, 2))

            # Kiểm tra xem trang có nội dung để đọc không
            page_info = page.evaluate("""() => {
                return {
                    scrollHeight: document.body.scrollHeight || 0,
                    viewportHeight: window.innerHeight,
                    hasContent: document.body.innerText.length > 100
                };
            }""")

            if not page_info['hasContent'] or page_info['scrollHeight'] < 100:
                print("Trang không có nội dung để đọc")
                return

            # Giai đoạn 1: Quét nhanh tổng quan (2-3 giây)
            self.quick_scan_overview(page)

            # Giai đoạn 2: Đọc chi tiết các phần quan trọng (3-5 giây)
            self.detailed_reading(page)

            # Giai đoạn 3: Scroll tự nhiên kết hợp đọc (2-4 giây)
            self.natural_scroll_reading(page)

            # Giai đoạn 4: Kiểm tra lại thông tin quan trọng (1-2 giây)
            self.final_review(page)

            print("✅ Hoàn thành mô phỏng đọc tự nhiên")

        except Exception as e:
            print(f"Exception trong simulate_reading_behavior: {e}")
            # Fallback về cuộn đơn giản nếu có lỗi
            self.simple_fallback_scroll(page)

    def quick_scan_overview(self, page):
        """Giai đoạn 1: Quét nhanh tổng quan trang"""
        try:
            print("👀 Quét nhanh tổng quan...")

            # Di chuyển chuột từ trên xuống dưới để "quét" nhanh
            viewport = page.viewport_size
            start_y = 100
            end_y = min(viewport['height'] - 100, 600)

            # Quét theo đường zigzag tự nhiên
            for y in range(start_y, end_y, 80):
                x = random.randint(200, viewport['width'] - 200)
                page.mouse.move(x, y)
                time.sleep(random.uniform(0.1, 0.2))

            time.sleep(random.uniform(0.5, 1))

        except Exception as e:
            print(f"Lỗi trong quick_scan_overview: {e}")

    def detailed_reading(self, page):
        """Giai đoạn 2: Đọc chi tiết các phần quan trọng"""
        try:
            print("📖 Đọc chi tiết nội dung...")

            # Danh sách các selector quan trọng để đọc
            reading_targets = [
                {'selectors': ['h1', '.product-title', '[data-testid="product-title"]'], 'priority': 'high', 'time': (1.5, 3)},
                {'selectors': ['.price', '.product-price', '[class*="price"]'], 'priority': 'high', 'time': (1, 2)},
                {'selectors': ['.rating', '.review-score', '[class*="rating"]'], 'priority': 'medium', 'time': (0.8, 1.5)},
                {'selectors': ['.description', '.product-description', '[class*="description"]'], 'priority': 'medium', 'time': (2, 4)},
                {'selectors': ['.seller-info', '.shop-info', '[class*="seller"]'], 'priority': 'low', 'time': (1, 2)}
            ]

            for target in reading_targets:
                self.simulate_reading_element(page, target)

        except Exception as e:
            print(f"Lỗi trong detailed_reading: {e}")

    def simulate_reading_element(self, page, target):
        """Mô phỏng đọc một element cụ thể"""
        try:
            # Tìm element đầu tiên có sẵn từ danh sách selectors
            element = None
            for selector in target['selectors']:
                if page.locator(selector).count() > 0:
                    element = page.locator(selector).first
                    break

            if not element:
                return

            # Lấy vị trí element
            box = element.bounding_box()
            if not box:
                return

            # Scroll để element hiển thị nếu cần
            element.scroll_into_view_if_needed()
            time.sleep(random.uniform(0.3, 0.6))

            # Mô phỏng đọc bằng cách di chuyển chuột theo text
            self.mouse_trace_text(page, box, target['priority'])

            # Thời gian đọc dựa trên độ ưu tiên
            read_time = random.uniform(*target['time'])
            time.sleep(read_time)

        except Exception as e:
            print(f"Lỗi trong simulate_reading_element: {e}")

    def mouse_trace_text(self, page, element_box, priority):
        """Di chuyển chuột theo dòng text như đang đọc"""
        try:
            x_start = element_box['x'] + 10
            x_end = element_box['x'] + element_box['width'] - 10
            y_start = element_box['y'] + 10
            y_end = element_box['y'] + element_box['height'] - 10

            # Số dòng để "đọc" dựa trên priority
            lines_to_read = {'high': 3, 'medium': 2, 'low': 1}[priority]

            # Di chuyển chuột theo từng dòng
            for line in range(lines_to_read):
                y_pos = y_start + (line * (y_end - y_start) / max(lines_to_read - 1, 1))

                # Di chuyển từ trái sang phải (đọc dòng)
                steps = random.randint(8, 15)
                for step in range(steps):
                    x_pos = x_start + (step * (x_end - x_start) / steps)
                    page.mouse.move(x_pos, y_pos)
                    time.sleep(random.uniform(0.05, 0.12))

                # Dừng cuối dòng
                time.sleep(random.uniform(0.1, 0.3))

        except Exception as e:
            print(f"Lỗi trong mouse_trace_text: {e}")

    def natural_scroll_reading(self, page):
        """Giai đoạn 3: Scroll tự nhiên kết hợp với đọc"""
        try:
            print("📜 Scroll tự nhiên kết hợp đọc...")

            viewport_height = page.viewport_size['height']
            scroll_step = viewport_height // 4  # Scroll 1/4 màn hình mỗi lần

            # Thực hiện 2-3 lần scroll nhỏ
            scroll_times = random.randint(2, 3)
            for i in range(scroll_times):
                # Scroll xuống một chút
                page.evaluate(f"window.scrollBy(0, {scroll_step})")
                time.sleep(random.uniform(0.4, 0.8))

                # Đọc nội dung ở vị trí hiện tại
                self.read_visible_content(page)

                # 30% khả năng scroll ngược lại "đọc lại"
                if random.random() < 0.3:
                    page.evaluate(f"window.scrollBy(0, -{scroll_step//2})")
                    time.sleep(random.uniform(0.3, 0.6))

        except Exception as e:
            print(f"Lỗi trong natural_scroll_reading: {e}")

    def read_visible_content(self, page):
        """Đọc nội dung hiện tại trên màn hình"""
        try:
            # Lấy vị trí viewport hiện tại
            viewport = page.viewport_size
            center_x = viewport['width'] // 2
            center_y = viewport['height'] // 2

            # Di chuyển chuột quanh khu vực trung tâm để "đọc"
            for _ in range(random.randint(3, 6)):
                x = center_x + random.randint(-200, 200)
                y = center_y + random.randint(-150, 150)
                page.mouse.move(x, y)
                time.sleep(random.uniform(0.1, 0.25))

        except Exception as e:
            print(f"Lỗi trong read_visible_content: {e}")

    def final_review(self, page):
        """Giai đoạn 4: Kiểm tra lại thông tin quan trọng"""
        try:
            print("🔍 Kiểm tra lại thông tin quan trọng...")

            # Hover vào các thông tin quan trọng
            important_selectors = [
                '.price, .product-price, [class*="price"]',
                '.rating, .review-score, [class*="rating"]',
                'img, .product-image, [class*="image"]'
            ]

            for selector in important_selectors:
                try:
                    if page.locator(selector).count() > 0:
                        element = page.locator(selector).first
                        element.hover()
                        time.sleep(random.uniform(0.3, 0.8))
                except:
                    continue

        except Exception as e:
            print(f"Lỗi trong final_review: {e}")

    def simple_fallback_scroll(self, page):
        """Fallback đơn giản nếu mô phỏng đọc gặp lỗi"""
        try:
            print("⚠️ Fallback về cuộn đơn giản...")

            # Cuộn đơn giản như cũ nhưng ngắn hơn
            scroll_height = page.evaluate("() => document.body.scrollHeight || 0")
            if scroll_height > 100:
                random_pos = int(random.uniform(0.3, 0.7) * scroll_height)
                page.evaluate(f"window.scrollTo(0, {random_pos})")
                time.sleep(random.uniform(1, 2))

                # Một vài cuộn nhỏ
                for _ in range(2):
                    page.evaluate("window.scrollBy(0, 150)")
                    time.sleep(random.uniform(0.5, 1))

        except Exception as e:
            print(f"Lỗi trong simple_fallback_scroll: {e}")

    def interleave_non_product_requests(self, page):
        """Xen kẽ request không phải sản phẩm để làm nhiễu pattern và tránh captcha"""
        try:
            print("🔀 Thực hiện request interleaving để tránh captcha...")

            # Danh sách các trang dummy theo yêu cầu
            all_dummy_pages = [
                "https://shopee.vn/flash_sale",
                "https://shopee.vn/m/ma-giam-gia",
                "https://shopee.vn/m/Shopee-Choice",
                "https://shopee.vn/m/giaithuongshopee",
                "https://help.shopee.vn/portal/4/vn/s",
                "https://shopee.vn/user/notifications/order",
                "https://shopee.vn/mall"
            ]

            # Use memory system to get best dummy pages
            best_pages = self.memory_system.get_best_dummy_pages(all_dummy_pages)
            selected_page = random.choice(best_pages)
            print(f"📄 Truy cập trang dummy: {selected_page}")

            # Truy cập nhanh trang dummy
            page.goto(selected_page, timeout=15000, wait_until="domcontentloaded")

            # Thực hiện một vài thao tác tự nhiên ngắn
            self.quick_dummy_interaction(page)

            # Thời gian nghỉ ngắn (1-2.5 giây)
            time.sleep(random.uniform(1, 2.5))

            # Record successful interleave
            self.memory_system.record_dummy_usage(selected_page, success=True)
            self.memory_system.record_interleave()

            print("✅ Hoàn thành request interleaving")

        except Exception as e:
            print(f"⚠️ Lỗi trong interleave_non_product_requests: {e}")
            # Record failed interleave
            if 'selected_page' in locals():
                self.memory_system.record_dummy_usage(selected_page, success=False)
            # Nếu có lỗi, chỉ nghỉ ngắn rồi tiếp tục
            time.sleep(random.uniform(0.5, 1))

    def quick_dummy_interaction(self, page):
        """Thực hiện tương tác nhanh trên trang dummy"""
        try:
            # Scroll nhẹ để trang load content
            page.evaluate("window.scrollBy(0, 300)")
            time.sleep(random.uniform(0.3, 0.6))

            # Di chuyển chuột ngẫu nhiên
            viewport = page.viewport_size
            x = random.randint(100, viewport['width'] - 100)
            y = random.randint(100, viewport['height'] - 100)
            page.mouse.move(x, y)

            # 30% khả năng hover vào element nào đó
            if random.random() < 0.3:
                try:
                    # Tìm element để hover (an toàn)
                    common_selectors = ['a', 'button', '.item', '.product']
                    for selector in common_selectors:
                        if page.locator(selector).count() > 0:
                            page.locator(selector).first.hover()
                            break
                except:
                    pass

        except Exception as e:
            print(f"Lỗi trong quick_dummy_interaction: {e}")

    def obfuscate_request_pattern(self, page, current_index, total_count):
        """Làm mờ pattern request để tránh detection"""
        try:
            # Thực hiện pattern breaking mỗi 4-7 sản phẩm
            if current_index > 0 and current_index % random.randint(4, 7) == 0:
                print(f"🎭 Thực hiện pattern obfuscation tại sản phẩm {current_index}/{total_count}")

                # Chọn ngẫu nhiên một hành vi pattern breaking
                pattern_breakers = [
                    self.simulate_search_behavior,
                    self.visit_random_category,
                    self.check_notifications_briefly,
                    self.browse_flash_sale_quickly
                ]

                # Thực hiện 1 pattern breaker ngẫu nhiên
                selected_breaker = random.choice(pattern_breakers)
                selected_breaker(page)

                # Record pattern break
                self.memory_system.record_pattern_break()

                print("✅ Hoàn thành pattern obfuscation")

        except Exception as e:
            print(f"⚠️ Lỗi trong obfuscate_request_pattern: {e}")

    def simulate_search_behavior(self, page):
        """Mô phỏng hành vi tìm kiếm"""
        try:
            print("🔍 Mô phỏng tìm kiếm...")

            # Truy cập trang tìm kiếm với từ khóa ngẫu nhiên
            search_keywords = ["điện thoại", "laptop", "quần áo", "giày dép", "túi xách", "đồng hồ"]
            keyword = random.choice(search_keywords)

            search_url = f"https://shopee.vn/search?keyword={keyword}"
            page.goto(search_url, timeout=15000, wait_until="domcontentloaded")

            # Scroll nhẹ như đang xem kết quả
            page.evaluate("window.scrollBy(0, 400)")
            time.sleep(random.uniform(0.8, 1.5))

        except Exception as e:
            print(f"Lỗi trong simulate_search_behavior: {e}")

    def visit_random_category(self, page):
        """Truy cập category ngẫu nhiên"""
        try:
            print("📂 Truy cập category...")

            # Truy cập trang categories
            page.goto("https://shopee.vn/m/Shopee-Choice", timeout=15000, wait_until="domcontentloaded")

            # Scroll và di chuyển chuột như đang browse
            page.evaluate("window.scrollBy(0, 500)")
            time.sleep(random.uniform(0.6, 1.2))

        except Exception as e:
            print(f"Lỗi trong visit_random_category: {e}")

    def check_notifications_briefly(self, page):
        """Kiểm tra notifications ngắn"""
        try:
            print("🔔 Kiểm tra notifications...")

            page.goto("https://shopee.vn/user/notifications/order", timeout=15000, wait_until="domcontentloaded")
            time.sleep(random.uniform(0.8, 1.5))

        except Exception as e:
            print(f"Lỗi trong check_notifications_briefly: {e}")

    def browse_flash_sale_quickly(self, page):
        """Browse flash sale nhanh"""
        try:
            print("⚡ Browse flash sale...")

            page.goto("https://shopee.vn/flash_sale", timeout=15000, wait_until="domcontentloaded")

            # Scroll và hover như đang xem deals
            page.evaluate("window.scrollBy(0, 300)")
            time.sleep(random.uniform(0.5, 1))

        except Exception as e:
            print(f"Lỗi trong browse_flash_sale_quickly: {e}")

    def find_valid_image_src(self, page):
        """Find product image with more detailed error reporting"""
        try:
            # Check for common issues that might indicate why image scraping is failing
            console_errors = page.evaluate("""() => {
                if (window.errorsFound) return window.errorsFound;
                window.errorsFound = [];
                const originalError = console.error;
                console.error = function(...args) {
                    window.errorsFound.push(args.join(' '));
                    originalError.apply(console, args);
                };
                return [];
            }""")
            
            if console_errors and len(console_errors) > 0:
                print(f"Console errors detected: {console_errors[:3]}")
            
            # Create detailed logging of DOM state to diagnose image selector issues
            dom_analysis = page.evaluate("""() => {
                const images = document.querySelectorAll('img');
                const videos = document.querySelectorAll('video');
                return {
                    totalImages: images.length,
                    hasVideo: videos.length > 0,
                    visibleImages: Array.from(images).filter(img => {
                        const rect = img.getBoundingClientRect();
                        return rect.width > 0 && rect.height > 0;
                    }).length,
                    firstFewSelectors: Array.from(images).slice(0, 5).map(img => {
                        return {
                            class: img.className,
                            src: img.src && img.src.substring(0, 50),
                            width: img.width,
                            height: img.height
                        };
                    })
                };
            }""")
            
            print(f"DOM analysis: {dom_analysis}")
            
            # Check auth state before starting
            if self.auth_paused:
                return None
                    
            # Check if processing stopped
            if not self.processing:
                return None
                    
            # Check for video with auth checks
            try:
                has_video = page.locator("video").count() > 0
            except Exception:
                # If error occurred, check if it's auth-related
                if self.auth_paused:
                    return None
                has_video = False
                    
            # Check auth again
            if self.auth_paused:
                return None
                    
            # Handle primary selector with auth checks
            try:
                imgs = page.locator("img.uXN1L5.lazyload")
                count = imgs.count()
                print(f"Primary selector 'img.uXN1L5.lazyload' found {count} matches")
            except Exception as selector_error:
                # If error occurred, check if it's auth-related
                print(f"Error with primary selector: {selector_error}")
                if self.auth_paused:
                    return None
                count = 0
                    
            # Check auth again
            if self.auth_paused:
                return None
                    
            if count > 0:
                try:
                    if has_video and count > 1:
                        # With video and multiple images, get the second image
                        src = imgs.nth(1).get_attribute("src")
                        if src:
                            print(f"Found second image (with video): {src}")
                            return src
                    else:
                        # No video or only one image, get the first
                        src = imgs.nth(0).get_attribute("src")
                        if src:
                            print(f"Found first image (without video): {src}")
                            return src
                except Exception as img_error:
                    # Log specific error with image extraction
                    print(f"Error extracting image src: {img_error}")
                    # Check if auth-related error
                    if self.auth_paused:
                        return None
            
            # Check auth before fallback selectors
            if self.auth_paused:
                return None
                    
            # Try more selectors with better logging
            print("Primary selector failed, trying alternatives...")
            alternate_selectors = [
                "img.XE47NpD",
                "img.YjzPnct",
                "img[alt*='product']",
                "div.JKvYqe > div > div > div > img",
                "div.stardust-carousel__item-inner img",
                # Add more generic selectors as ultimate fallbacks
                ".product-image img",
                ".product-detail img",
                ".product img",
                "img[src*='product']",
                "img[src*='image']" 
            ]
                
            for selector in alternate_selectors:
                # Check auth before each selector
                if self.auth_paused:
                    return None
                        
                try:
                    alternate_imgs = page.locator(selector)
                    count = alternate_imgs.count()
                    print(f"Alternative selector '{selector}' found {count} matches")
                    
                    for i in range(count):
                        # Check auth between each image
                        if self.auth_paused:
                            return None
                                
                        src = alternate_imgs.nth(i).get_attribute("src")
                        if src and len(src) > 20:
                            print(f"Found valid image with selector {selector}: {src}")
                            return src
                except Exception:
                    # Ignore errors for alternative selectors
                    continue
            
            # Try a completely generic approach as last resort
            try:
                all_images = page.evaluate("""() => {
                    const images = Array.from(document.querySelectorAll('img'));
                    return images
                        .filter(img => img.width > 200 && img.height > 200)
                        .filter(img => img.src && img.src.includes('http'))
                        .map(img => img.src)
                        .slice(0, 5);
                }""")
                
                if all_images and len(all_images) > 0:
                    print(f"Found generic large images: {all_images}")
                    return all_images[0]
            except Exception as generic_error:
                print(f"Error in generic image search: {generic_error}")
            
            print("No valid image found after trying all selectors.")
            return None
        except Exception as e:
            print(f"Error in find_valid_image_src: {e}")
            return None

    def show_message(self, title, message):
        msg = QMessageBox()
        msg.setIcon(QMessageBox.Icon.Information)
        msg.setWindowTitle(title)
        msg.setText(message)
        msg.setStandardButtons(QMessageBox.StandardButton.Ok)
        msg.exec()

    def col2num(self, col_str):
        num = 0
        for c in col_str:
            if c in string.ascii_letters:
                num = num * 26 + (ord(c.upper()) - ord('A')) + 1
        return num

    def save_results_to_file(self, results):
        if not results:
            return
        try:
            os.makedirs(os.path.dirname(self.log_file_path), exist_ok=True)
            with open(self.log_file_path, "a", encoding="utf-8") as f:
                f.write(f"\n--- Kết quả crawl lúc {time.strftime('%Y-%m-%d %H:%M:%S')} ---\n")
                for row_num, img_url in results.items():
                    f.write(f"Dòng {row_num}: {img_url}\n")
                f.write(f"Tổng số kết quả: {len(results)}\n")
                f.write("-----------------------------------\n")
            print(f"Đã lưu {len(results)} kết quả vào file: {self.log_file_path}")
        except Exception as e:
            print(f"Không thể lưu kết quả vào file: {str(e)}")

    def cleanup_processing(self):
        """Clean up all processing resources"""
        self.processing = False
        self.confirm_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        
        # Clean up timer - make sure this runs in the main thread
        if hasattr(self, 'update_timer') and self.update_timer.isActive():
            self.update_timer.stop()
        
        # Process any pending events
        QApplication.processEvents()

    def show_safe_message(self, title, message, cleanup_callback=None):
        """Show message box safely without causing repaints"""
        try:
            # Create message box without parent to avoid paint conflicts
            msg = QMessageBox()
            msg.setWindowTitle(title)
            msg.setText(message)
            msg.setIcon(QMessageBox.Icon.Information)
            msg.setStandardButtons(QMessageBox.StandardButton.Ok)
            
            # Connect cleanup callback if provided
            if cleanup_callback:
                msg.finished.connect(cleanup_callback)
            
            # Show non-modal
            msg.show()
            
        except Exception as e:
            print(f"Error showing message: {e}")
            # Fallback to status label
            self.status_label.setText(message)
            if cleanup_callback:
                cleanup_callback()

    def resume_after_auth(self):
        """Resume processing after authentication with improved feedback"""
        print("User clicked resume after auth")
        self.auth_paused = False
        self.auth_status_label.setText("✅ Đã xác thực xong, đang tiếp tục")
        self.auth_resume_button.setEnabled(False)
        self.processing = True  # Ensure processing is still active
        
        # Reset the status display for all unprocessed links
        # Find all table rows with "⌛ Đợi xác thực hoàn tất..." status
        for row in range(self.results_table.rowCount()):
            item = self.results_table.item(row, 2)  # Status column
            if item and "⌛ Đợi xác thực hoàn tất..." in item.text():
                # Change back to waiting status
                item.setText("Đang chờ xử lý...")
        
        # Force UI update
        QApplication.processEvents()

    def wait_for_url_stability(self, page, timeout_seconds=3):
        """Wait for URL to stabilize (no more redirects/changes)"""
        start_time = time.time()
        last_url = page.url
        print(f"Starting URL stability check from: {last_url}")
        
        # Check auth state and processing state immediately
        if self.auth_paused:
            print("Auth paused before stability check")
            return False
        
        if not self.processing:
            print("Processing stopped before stability check")
            return False
        
        # Poll URL with frequent checks
        while time.time() - start_time < timeout_seconds:
            # First check if we're already in auth_paused state
            if self.auth_paused:
                print("Auth paused during stability check")
                return False
                
            # Check if processing stopped
            if not self.processing:
                print("Processing stopped during stability check")
                return False
                
            try:
                current_url = page.url
                
                # Direct check ONLY for actual captcha/login URLs (more specific matching)
                if ("buyer/login" in current_url) or ("verify/captcha" in current_url):
                    auth_type = "đăng nhập" if "buyer/login" in current_url else "captcha"
                    print(f"Auth page detected during stability check: {auth_type} - {current_url}")
                    self.auth_status_label.setText(f"⚠️ Phát hiện trang {auth_type} Shopee. Vui lòng xử lý và nhấn OK để tiếp tục.")
                    self.auth_resume_button.setEnabled(True)
                    self.manage_browser_window(page, "maximize")
                    self.auth_paused = True
                    return False
                    
                if current_url != last_url:
                    # URL changed, reset timer and update last_url
                    print(f"URL changed during stability check from {last_url} to {current_url}")
                    last_url = current_url
                    start_time = time.time()
                
            except Exception as e:
                print(f"Error during stability check: {e}")
            
            # Very small sleep to allow more frequent checks
            time.sleep(0.2)
        
        # One final check after the waiting period
        try:
            final_url = page.url
            print(f"Final URL after stability check: {final_url}")
            
            if ("buyer/login" in final_url) or ("verify/captcha" in final_url):
                auth_type = "đăng nhập" if "buyer/login" in final_url else "captcha"
                print(f"Auth detected at final stability check: {auth_type}")
                self.auth_status_label.setText(f"⚠️ Phát hiện trang {auth_type} Shopee. Vui lòng xử lý và nhấn OK để tiếp tục.")
                self.auth_resume_button.setEnabled(True)
                self.manage_browser_window(page, "maximize")
                self.auth_paused = True
                return False
        except Exception as e:
            print(f"Error in final stability check: {e}")
        
        # If we got here, the URL has been stable for timeout_seconds
        return True

    def reset_ui(self):
        """Reset the entire UI to its initial state"""
        # Reset all inputs
        self.sheet_link_edit.clear()
        self.sheet_combo.clear()
        self.results_table.setRowCount(0)
        self.progress_bar.setValue(0)
        self.status_label.setText("")
        self.auth_status_label.setText("URL checking...")
        self.auth_resume_button.setEnabled(False)
        self.total_label.setText("Tổng số link cần xử lý: 0")
        self.error_label.setText("Số link lỗi: 0")
        
        # Reset internal state variables
        self.error_count = 0
        self.temp_results = []
        self.progress_messages = []
        self.processed_rows_cache = set()  # Reset processed_rows_cache
        self.confirm_button.setEnabled(False)
        self.stop_button.setEnabled(False)
        self.processing = False
        self.auth_paused = False
        self.current_url = ""
        
        # Ensure timer is stopped
        if hasattr(self, 'update_timer') and self.update_timer.isActive():
            self.update_timer.stop()
        
        # Process events to ensure UI updates
        QApplication.processEvents()

    def update_ui_status(self, message, percent=None):
        """Update UI status with better event processing"""
        try:
            if percent is not None:
                self.status_label.setText(f"{percent}%")
                self.progress_bar.setValue(percent)
            else:
                if message.startswith("Đã") or message.startswith("Lỗi") or "cập nhật" in message:
                    self.status_label.setText(message)
            
            # Process events to ensure UI stays responsive
            QApplication.processEvents()
        except Exception as e:
            print(f"Error updating UI status: {e}")

    def process_sheet(self):
        try:
            self.error_count = 0
            self.temp_results = []
            self.progress_messages = []
            self.results_table.setRowCount(0)
            self.progress_bar.setValue(0)
            self.status_label.setText("")
            # Đảm bảo processed_rows_cache đã tồn tại
            if not hasattr(self, 'processed_rows_cache'):
                self.processed_rows_cache = set()
            else:
                # Reset processed_rows_cache mỗi khi bắt đầu process_sheet
                self.processed_rows_cache = set()  
            
            sheet_name = self.sheet_combo.currentText()
            status_col = self.status_col_edit.text().strip()
            url_col = self.url_col_edit.text().strip()
            img_col = self.img_col_edit.text().strip()
            created_date_col = self.created_date_col_edit.text().strip()  # Lấy giá trị cột Created date
            
            # Xác định index cho các cột
            url_idx = self.col2num(url_col) - 1
            
            # Kiểm tra xem người dùng đã nhập giá trị cho cột nào
            use_status_condition = status_col != ""
            use_created_date_condition = created_date_col != ""
            
            # Chỉ tính toán index cho các cột được sử dụng
            status_idx = self.col2num(status_col) - 1 if use_status_condition else -1
            img_idx = self.col2num(img_col) - 1 if use_status_condition else -1
            created_date_idx = self.col2num(created_date_col) - 1 if use_created_date_condition else -1
            
            # Kiểm tra nếu không có điều kiện nào được cung cấp
            if not use_status_condition and not use_created_date_condition:
                QMessageBox.warning(self, "Lỗi", "Vui lòng nhập ít nhất một điều kiện (Cột hình ảnh hoặc Cột Created date).")
                return
            
            # Xử lý sheet_id với nhiều định dạng URL khác nhau
            sheet_link = self.sheet_link_edit.text().strip()
            if not sheet_link:
                raise ValueError("Không tìm thấy link Google Sheet")
                
            # Nếu link đã là spreadsheet ID (không chứa / hoặc .)
            if '/' not in sheet_link and '.' not in sheet_link and len(sheet_link) >= 25:
                sheet_id = sheet_link
            else:
                # Thử các pattern khác nhau để trích xuất sheet_id
                patterns = [
                    r"/d/([a-zA-Z0-9-_]+)",  # Format: /d/SPREADSHEET_ID
                    r"spreadsheets/d/([a-zA-Z0-9-_]+)",  # Format: spreadsheets/d/SPREADSHEET_ID
                    r"key=([a-zA-Z0-9-_]+)"  # Format: key=SPREADSHEET_ID
                ]
                
                sheet_id = None
                for pattern in patterns:
                    match = re.search(pattern, sheet_link)
                    if match:
                        sheet_id = match.group(1)
                        break
                        
                if not sheet_id:
                    # Sử dụng toàn bộ link như một ID nếu không tìm thấy pattern
                    sheet_id = sheet_link
            
            gsm = GoogleSheetManager(
                auth_type='oauth', 
                credentials_data=OAUTH_CREDENTIALS_B64,
                all_oauth_credentials=ALTERNATIVE_CREDENTIALS
            )
            sh = gsm.open_by_key(sheet_id)
            worksheet = sh.worksheet(sheet_name)
            
            self.update_ui_status("Đang quét các dòng cần xử lý...")
            all_values = worksheet.get_all_values()
            rows_to_process = []
            for idx, row in enumerate(all_values, start=1):
                if idx < 4:
                    continue
                    
                # Kiểm tra các điều kiện dựa trên cột được nhập
                condition1 = False
                condition2 = False
                
                # Chỉ kiểm tra điều kiện cột ảnh nếu người dùng đã nhập cột này
                if use_status_condition and len(row) >= status_idx + 1:
                    condition1 = row[status_idx].strip() == "#N/A"
                
                # Chỉ kiểm tra điều kiện cột Created date nếu người dùng đã nhập cột này
                if use_created_date_condition and len(row) >= created_date_idx + 1:
                    created_date_value = row[created_date_idx].strip()
                    condition2 = created_date_value == "" or created_date_value == "#N/A"
                
                # Xác định dòng cần xử lý dựa trên điều kiện có sẵn
                process_row = False
                if use_status_condition and use_created_date_condition:
                    # Nếu cả hai điều kiện được nhập: áp dụng logic HOẶC
                    process_row = condition1 or condition2
                elif use_status_condition:
                    # Nếu chỉ điều kiện cột ảnh được nhập
                    process_row = condition1
                elif use_created_date_condition:
                    # Nếu chỉ điều kiện cột Created date được nhập
                    process_row = condition2
                
                # Đảm bảo processed_rows_cache đã tồn tại trước khi kiểm tra
                if not hasattr(self, 'processed_rows_cache'):
                    self.processed_rows_cache = set()
                
                if process_row and idx not in self.processed_rows_cache and len(row) >= url_idx + 1:
                    product_link = row[url_idx].strip()
                    rows_to_process.append((idx, product_link))
                    
            if not rows_to_process:
                self.progress_bar.setValue(0)
                QTimer.singleShot(100, lambda: self.show_safe_message(
                    "Thông báo", 
                    "Không có dòng nào cần xử lý.",
                    cleanup_callback=self.cleanup_processing
                ))
                return
                
            self.total_label.setText(f"Tổng số link cần xử lý: {len(rows_to_process)}")
            self.results_table.setRowCount(len(rows_to_process))
            for i, (row_num, product_link) in enumerate(rows_to_process):
                self.results_table.setItem(i, 0, QTableWidgetItem(str(row_num)))
                self.results_table.setItem(i, 1, QTableWidgetItem(product_link))
                self.results_table.setItem(i, 2, QTableWidgetItem("Đang chờ xử lý..."))
                self.results_table.setItem(i, 3, QTableWidgetItem(""))
            self.update_ui_status("", 0)
            results = []
            self.update_ui_status("Đang chuẩn bị trình duyệt...")
            
            with sync_playwright() as p:
                try:
                    # Khởi tạo context và đảm bảo browser_context tồn tại
                    context, headless_mode = self.launch_context(p, headless_mode=False)
                    if not hasattr(self, 'browser_context'):
                        self.browser_context = None
                    self.browser_context = context
                    
                    total_rows = len(rows_to_process)
                    current_idx = 0  # Track current position in the list

                    # Initialize anti-captcha counters with adaptive frequencies
                    interleave_freq, pattern_freq = self.memory_system.get_adaptive_frequencies()
                    products_since_last_interleave = 0
                    products_since_last_pattern_break = 0

                    # Process links until we're done or stop is requested
                    while current_idx < total_rows and self.processing:
                        row_num, product_link = rows_to_process[current_idx]
                        progress = int((current_idx / total_rows) * 100)
                        self.progress_bar.setValue(progress)
                        self.update_ui_status("", progress)
                        print(f"\n===== Xử lý dòng {row_num} với link sản phẩm: {product_link} =====")
                        
                        # Update auth status label
                        QMetaObject.invokeMethod(
                            self.auth_status_label, 
                            "setText", 
                            Qt.ConnectionType.QueuedConnection,
                            Q_ARG(str, f"Đang xử lý dòng {row_num}")
                        )
                        
                        table_row = current_idx
                        status_item = self.results_table.item(table_row, 2)
                        status_item.setText("Đang xử lý...")
                        QApplication.processEvents()
                        
                        # Reset auth_paused state before each new page load
                        self.auth_paused = False
                        
                        # Create new page
                        page = context.new_page()
                        
                        # Check if processing was stopped
                        if not self.processing:
                            page.close()
                            self.update_ui_status("Đã dừng xử lý")
                            break
                        
                        # Set up URL monitoring
                        self.setup_url_monitoring(page)
                        self.current_url = product_link  # Initialize current URL

                        # Anti-captcha strategy: Adaptive request interleaving
                        if products_since_last_interleave >= random.randint(*interleave_freq):
                            self.interleave_non_product_requests(page)
                            products_since_last_interleave = 0

                        # Anti-captcha strategy: Adaptive pattern obfuscation
                        if products_since_last_pattern_break >= random.randint(*pattern_freq):
                            self.obfuscate_request_pattern(page, current_idx, total_rows)
                            products_since_last_pattern_break = 0
                        
                        # Load page with retry
                        status = self.load_page_with_retry(page, product_link)
                        
                        # If auth was detected, wait for user to resolve without any timeout
                        if self.auth_paused or status in ["login", "captcha", "auth_required"]:
                            auth_type = "đăng nhập" if status == "login" else "captcha"
                            print(f"Detected {auth_type}, waiting for user to resolve...")
                            
                            # Update ALL subsequent links to show we're waiting
                            for i in range(current_idx + 1, total_rows):
                                wait_item = self.results_table.item(i, 2)
                                wait_item.setText("⌛ Đợi xác thực hoàn tất...")
                            
                            # Update current link status
                            status_item.setText(f"⚠️ Đang chờ xác thực ({auth_type})...")
                            
                            # Wait indefinitely for user to click OK without any timeout
                            while self.auth_paused and self.processing:
                                QApplication.processEvents()
                                time.sleep(0.5)
                            
                            # If processing was stopped while waiting for auth
                            if not self.processing:
                                page.close()
                                self.update_ui_status("Đã dừng xử lý")
                                break
                            
                            # If user resolved the auth, continue with the same link
                            if self.processing:
                                # Reset status for this link and try again with the SAME INDEX
                                status_item.setText("Đang thử lại sau xác thực...")
                                page.close()
                                continue  # This is crucial - we don't increment current_idx
                        
                        # If we got a page loading error or 404, move to next link (tối ưu skip nhanh)
                        if status in ["not_found", "failed", "stopped", "skipped"] or status.startswith(("timeout:", "navigation:", "failed:")):
                            print(f"[Dòng {row_num}] Không thể tải trang: {status}")
                            results.append((row_num, f"Không thể tải trang: {status}"))
                            self.temp_results.append((row_num, f"Không thể tải trang: {status}"))
                            status_item.setText(f"Lỗi: {status}")
                            self.results_table.setItem(table_row, 3, QTableWidgetItem("N/A"))
                            self.error_count += 1
                            self.error_label.setText(f"Số link lỗi: {self.error_count}")
                            page.close()
                            current_idx += 1  # Move to next link
                            continue
                        
                        # Check if product exists (tối ưu với timeout ngắn hơn)
                        try:
                            # Sử dụng evaluate thay vì inner_text để nhanh hơn
                            product_not_found = page.evaluate("""() => {
                                return document.body.innerText.includes("Sản phẩm này không tồn tại");
                            }""")

                            if product_not_found:
                                print(f"[Dòng {row_num}] Sản phẩm này không tồn tại")
                                results.append((row_num, "Sản phẩm này không tồn tại"))
                                self.temp_results.append((row_num, "Sản phẩm này không tồn tại"))
                                status_item.setText("Sản phẩm không tồn tại")
                                self.results_table.setItem(table_row, 3, QTableWidgetItem("N/A"))
                                self.error_count += 1
                                self.error_label.setText(f"Số link lỗi: {self.error_count}")
                                page.close()
                                current_idx += 1  # Move to next link
                                continue
                        except Exception as e:
                            # Check if auth is needed during content check
                            if self.auth_paused:
                                page.close()
                                continue  # Retry same link

                            # If not auth, it's a general error
                            print(f"Error checking product existence: {e}")
                        
                        # Check if auth is needed before scrolling
                        if self.auth_paused:
                            page.close()
                            continue  # Retry same link (don't increment current_idx)
                        
                        # Check processing state before scrolling
                        if not self.processing:
                            page.close()
                            self.update_ui_status("Đã dừng xử lý")
                            break
                        
                        # Perform intelligent reading simulation
                        self.simulate_reading_behavior(page)
                        
                        # Check if auth is needed after scrolling
                        if self.auth_paused:
                            page.close()
                            continue  # Retry same link
                        
                        # Apply stealth
                        stealth.apply_stealth_sync(page)
                        
                        # Try to find image
                        try:
                            # Check again for auth or stop
                            if self.auth_paused:
                                page.close()
                                continue  # Retry same link
                                
                            if not self.processing:
                                page.close()
                                self.update_ui_status("Đã dừng xử lý")
                                break
                            
                            # Find image with selectors (tối ưu timeout)
                            found_selector = False
                            for selector in ["img.uXN1L5.lazyload", "img.XE47NpD", "img.YjzPnct", "img[alt*='product']"]:
                                try:
                                    if page.wait_for_selector(selector, timeout=3000):  # Tối ưu: giảm từ 5000ms xuống 3000ms
                                        found_selector = True
                                        break
                                except:
                                    # Check for auth during waiting
                                    if self.auth_paused:
                                        break

                                    # Check if processing is still active
                                    if not self.processing:
                                        break
                            
                            # Process results
                            valid_src = None
                            if found_selector and self.processing and not self.auth_paused:
                                valid_src = self.find_valid_image_src(page)
                            
                            # Update results based on image find
                            if valid_src:
                                print(f"[Dòng {row_num}] Link ảnh: {valid_src}")
                                results.append((row_num, valid_src))
                                self.temp_results.append((row_num, valid_src))
                                status_item.setText("Thành công")
                                self.results_table.setItem(table_row, 3, QTableWidgetItem(valid_src))
                                # Record success in memory system
                                self.memory_system.record_success()
                            else:
                                # Check if we paused for auth during image finding
                                if self.auth_paused:
                                    page.close()
                                    continue  # Retry same link
                                    
                                # Check if we stopped processing
                                if not self.processing:
                                    page.close()
                                    self.update_ui_status("Đã dừng xử lý")
                                    break
                                    
                                # No valid image found
                                print(f"[Dòng {row_num}] Không tìm thấy ảnh hợp lệ.")
                                results.append((row_num, "Không tìm thấy ảnh hợp lệ"))
                                self.temp_results.append((row_num, "Không tìm thấy ảnh hợp lệ"))
                                status_item.setText("Không tìm thấy ảnh")
                                self.results_table.setItem(table_row, 3, QTableWidgetItem("N/A"))
                                self.error_count += 1
                                self.error_label.setText(f"Số link lỗi: {self.error_count}")
                        except Exception as e:
                            print(f"[Dòng {row_num}] Lỗi khi tìm ảnh: {e}")
                            
                            # Check if auth-related error
                            if self.auth_paused:
                                page.close()
                                continue  # Retry same link
                                
                            # Regular error handling
                            results.append((row_num, f"Lỗi khi tìm ảnh: {e}"))
                            self.temp_results.append((row_num, f"Lỗi khi tìm ảnh: {e}"))
                            status_item.setText(f"Lỗi: {str(e)}")
                            self.results_table.setItem(table_row, 3, QTableWidgetItem("N/A"))
                            self.error_count += 1
                            self.error_label.setText(f"Số link lỗi: {self.error_count}")
                        
                        # Link processed successfully, close page and move to next
                        time.sleep(random.uniform(2, 3))  # Tối ưu: giảm từ 4-7 xuống 2-3 giây
                        page.close()
                        current_idx += 1  # Move to next link
                        products_since_last_interleave += 1  # Increment counter for anti-captcha
                        products_since_last_pattern_break += 1  # Increment pattern break counter
                    
                    # Close context when done
                    context.close()
                    
                    # Update Google Sheet with results if not stopped
                    if self.processing and results:
                        self.update_ui_status("Đang cập nhật Google Sheet...")
                        
                        # Kiểm tra người dùng đã nhập trường nào
                        use_status_condition = status_col != ""
                        use_created_date_condition = created_date_col != ""
                        
                        # Create batch update for better performance
                        batch_data = []
                        for row_num, img_url in results:
                            # Only update if img_url is a valid URL (not an error message)
                            if img_url and not img_url.startswith(("Sản phẩm", "Không thể", "Lỗi", "Không")):
                                batch_data.append({"range": f"{img_col}{row_num}", "values": [[img_url]]})
                                
                                # Cập nhật cột hình ảnh chỉ khi người dùng đã nhập trường này
                                if use_status_condition:
                                    batch_data.append({"range": f"{status_col}{row_num}", "values": [[f'=IMAGE({img_col}{row_num})']]})
                        
                        # Process in chunks to avoid API limits and UI freezing
                        if batch_data:
                            total_chunks = (len(batch_data) + 49) // 50  # Ceiling division by 50
                            self.update_ui_status(f"Đang cập nhật Google Sheet (0/{total_chunks} chunks)...")
                            
                            try:
                                for i in range(0, len(batch_data), 50):
                                    # Check if processing was stopped during upload
                                    if not self.processing:
                                        self.update_ui_status("Đã dừng cập nhật")
                                        break
                                    
                                    chunk = batch_data[i:i+50]
                                    chunk_num = (i // 50) + 1
                                    self.update_ui_status(f"Đang cập nhật Google Sheet ({chunk_num}/{total_chunks} chunks)...")
                                    
                                    # Allow UI to update before each chunk
                                    QApplication.processEvents()
                                    
                                    # Upload chunk
                                    worksheet.batch_update(chunk, value_input_option="USER_ENTERED")
                                    
                                    # Allow UI to update after each chunk
                                    QApplication.processEvents()
                                    
                                # Final UI update
                                self.update_ui_status("Đã hoàn thành cập nhật Google Sheet")
                                QApplication.processEvents()
                            except Exception as upload_error:
                                print(f"Lỗi khi cập nhật Google Sheet: {upload_error}")
                                self.update_ui_status(f"Lỗi khi cập nhật: {str(upload_error)}")
                                QApplication.processEvents()
                        
                        # Instead of using a timer to show completion dialog, call directly 
                        # from the main thread after ensuring UI is updated
                        QApplication.processEvents()
                        self.progress_bar.setValue(100)
                        self.update_ui_status("Hoàn thành", 100)
                        QApplication.processEvents()
                        
                        # Fix: Use existing method to show completion dialog
                        self.completion_signal.emit(len(results))
                        
                        # Clean up and save results
                        self.save_results_to_file(dict(results))

                        # Finalize memory system learning
                        self.memory_system.finalize_session()
                    else:
                        # Nothing to update, just show completion
                        self.progress_bar.setValue(100)
                        self.update_ui_status("Hoàn thành - không có kết quả để cập nhật", 100)
                        QApplication.processEvents()
                        self.completion_signal.emit(0)
                except Exception as ex:
                    import traceback
                    error_details = traceback.format_exc()
                    print(f"Lỗi chi tiết: {error_details}")
                    self.update_ui_status(f"Lỗi: {str(ex)}")
                    self.show_safe_message("Lỗi", f"Có lỗi xảy ra: {str(ex)}\n\nChi tiết: {error_details[:500]}")
                finally:
                    self.cleanup_processing()
        
        except Exception as ex:
            import traceback
            error_details = traceback.format_exc()
            print(f"Lỗi chi tiết: {error_details}")
            self.update_ui_status(f"Lỗi: {str(ex)}")
            self.show_safe_message("Lỗi", f"Có lỗi xảy ra: {str(ex)}\n\nChi tiết: {error_details[:500]}")
        finally:
            self.cleanup_processing()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = ImageScrapingWidget()
    window.show()
    sys.exit(app.exec())