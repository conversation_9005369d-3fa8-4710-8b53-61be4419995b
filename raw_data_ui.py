import sys
import json
import os
import re
import unicodedata
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                            QHBoxLayout, QPushButton, QLabel, QLineEdit,
                            QTableWidget, QTableWidgetItem, QHeaderView,
                            QMessageBox, QFileDialog, QGroupBox, QFormLayout,
                            QSpinBox, QCheckBox, QTabWidget, QTextEdit,
                            QComboBox, QScrollArea, QSizePolicy, QDialog,
                            QGridLayout, QRadioButton, QProgressBar, QToolButton,
                            QMenu)
from PyQt6.QtCore import Qt, pyqtSlot, QSize, QThread, pyqtSignal, QTimer, QEvent
from PyQt6.QtGui import QIcon, QFont, QMovie

from raw_data_core import RawDataCore

def normalize_header(text):
    """
    Chuẩn hóa tên header tiếng Việt: bỏ dấu, xóa khoảng trống, chuyển về lowercase
    """
    if not text:
        return ""

    # Chuyển về Unicode NFD để tách dấu
    text = unicodedata.normalize('NFD', text)
    # Loại bỏ các ký tự dấu
    text = ''.join([c for c in text if not unicodedata.combining(c)])
    # Xóa khoảng trống và chuyển về lowercase
    text = re.sub(r'\s+', '', text).lower()
    return text

def extract_sheet_id_from_url(url):
    """
    Trích xuất Spreadsheet ID từ URL Google Sheets
    """
    if not url:
        return ""

    # Pattern cho URL Google Sheets
    patterns = [
        r'https://docs\.google\.com/spreadsheets/d/([a-zA-Z0-9_-]+)',  # Link chia sẻ thông thường
        r'https://docs\.google\.com/spreadsheets/d/e/([a-zA-Z0-9_-]+)', # Link xuất bản
        r'([a-zA-Z0-9_-]{25,})'  # ID trực tiếp (giả định >= 25 ký tự)
    ]

    for pattern in patterns:
        match = re.search(pattern, url)
        if match:
            return match.group(1)

    # Nếu không tìm thấy theo pattern, trả về url gốc (có thể là ID trực tiếp)
    return url


class WorkerThread(QThread):
    """Worker thread để chạy các tác vụ nặng"""
    progress_update = pyqtSignal(str)  # Signal để cập nhật trạng thái
    finished_signal = pyqtSignal(bool, object)  # Signal khi hoàn thành (success, result)
    error_signal = pyqtSignal(str)  # Signal khi có lỗi

    def __init__(self, task_type, *args, **kwargs):
        super().__init__()
        self.task_type = task_type
        self.args = args
        self.kwargs = kwargs
        self.raw_data_core = None

    def set_raw_data_core(self, core):
        """Thiết lập raw_data_core instance"""
        self.raw_data_core = core

    def run(self):
        """Chạy tác vụ trên background thread"""
        try:
            if self.task_type == "import":
                self.run_import_task()
            elif self.task_type == "process":
                self.run_process_task()
            elif self.task_type == "load_sheets":
                self.run_load_sheets_task()
        except Exception as e:
            self.error_signal.emit(str(e))

    def run_import_task(self):
        """Chạy tác vụ import"""
        target_spreadsheet_id, source_data, target_sheet, mode = self.args
        normalize_header_func = self.kwargs.get('normalize_header_func')

        self.progress_update.emit("Đang tạo mapping cột")

        # Tạo progress callback để cập nhật UI
        def progress_callback(message):
            self.progress_update.emit(message)

        result = self.raw_data_core.process_data_import(
            target_spreadsheet_id,
            source_data,
            target_sheet,
            column_mapping=None,
            normalize_header_func=normalize_header_func,
            mode=mode,
            progress_callback=progress_callback
        )

        self.finished_signal.emit(True, result)

    def run_process_task(self):
        """Chạy tác vụ xử lý dữ liệu"""
        spreadsheet_id, sheet_name, rules_to_apply = self.args

        self.progress_update.emit("Đang áp dụng quy tắc xử lý")

        result = self.raw_data_core.process_imported_data_with_rules(
            spreadsheet_id,
            sheet_name,
            rules_to_apply
        )

        self.finished_signal.emit(True, result)

    def run_load_sheets_task(self):
        """Chạy tác vụ tải sheets và headers"""
        spreadsheet_id, sheet_name = self.args

        self.progress_update.emit("Đang tải danh sách sheet...")

        # Lấy danh sách sheet
        sheet_list = self.raw_data_core.get_sheet_list(spreadsheet_id)

        self.progress_update.emit("Đang tải headers...")

        # Lấy headers (sử dụng dòng 2 mặc định cho UI)
        headers = None
        if sheet_name:
            headers, _ = self.raw_data_core.get_sheet_headers(spreadsheet_id, sheet_name, header_row=2)

        result = {
            'sheet_list': sheet_list,
            'headers': headers,
            'sheet_name': sheet_name
        }

        self.finished_signal.emit(True, result)


class LoadingDialog(QDialog):
    """Dialog hiển thị trạng thái loading với thông tin chi tiết"""

    def __init__(self, parent=None, title="Đang xử lý..."):
        super().__init__(parent)
        self.setWindowTitle(title)
        self.setModal(True)
        self.setFixedSize(400, 200)
        self.setWindowFlags(Qt.WindowType.Dialog | Qt.WindowType.CustomizeWindowHint | Qt.WindowType.WindowTitleHint)

        # Layout chính
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # Label tiêu đề
        self.title_label = QLabel(title)
        self.title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        font = self.title_label.font()
        font.setPointSize(12)
        font.setBold(True)
        self.title_label.setFont(font)
        layout.addWidget(self.title_label)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 0)  # Indeterminate progress
        layout.addWidget(self.progress_bar)

        # Label trạng thái chi tiết
        self.status_label = QLabel("Đang khởi tạo...")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.status_label.setWordWrap(True)
        layout.addWidget(self.status_label)

        # Spacer
        layout.addStretch()

        # Timer để cập nhật animation
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_animation)
        self.animation_dots = 0
        self.base_status = "Đang khởi tạo"

    def set_status(self, status):
        """Cập nhật trạng thái hiển thị"""
        self.base_status = status
        self.status_label.setText(status)

    def start_animation(self):
        """Bắt đầu animation loading"""
        self.timer.start(500)  # Cập nhật mỗi 500ms

    def stop_animation(self):
        """Dừng animation loading"""
        self.timer.stop()

    def update_animation(self):
        """Cập nhật animation dots"""
        self.animation_dots = (self.animation_dots + 1) % 4
        dots = "." * self.animation_dots
        self.status_label.setText(f"{self.base_status}{dots}")


class RawDataUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.raw_data_core = RawDataCore()
        self.worker_thread = None  # Worker thread cho các tác vụ nặng
        self.init_ui()

    def init_ui(self):
        # Thiết lập cửa sổ chính
        self.setWindowTitle("Google Sheet Data Import")
        self.setMinimumSize(600, 500)

        # Widget chính
        central_widget = QWidget()
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        self.setCentralWidget(central_widget)

        # Tạo top bar với nút Back và Context menu
        top_bar = QHBoxLayout()
        btn_back = QPushButton("⬅️ Trở về giao diện chính")
        btn_back.setFixedSize(150, 40)
        btn_back.setStyleSheet("border: none; font-size: 12px; font-weight: bold;")
        btn_back.clicked.connect(self.handle_back)
        top_bar.addWidget(btn_back, alignment=Qt.AlignmentFlag.AlignLeft)

        self.context_button = QToolButton()
        self.context_button.setText("Chuyển đến ➡️")
        self.context_button.setFixedSize(100, 30)
        self.context_button.setStyleSheet("""
            QToolButton {
                border: none;
                background: transparent;
                font-size: 12px;
                font-weight: bold;
            }
            QToolButton::menu-indicator {
                image: none;
                width: 0px;
            }
        """)
        self.context_button.installEventFilter(self)
        self.context_button.setPopupMode(QToolButton.ToolButtonPopupMode.InstantPopup)
        menu = QMenu(self.context_button)
        menu.addAction("Data Scraping", lambda: self.goto_other_program("autoshopee_scraping"))
        menu.addAction("Import Data", lambda: self.goto_other_program("import_data"))
        menu.addAction("Internal Data", lambda: self.goto_other_program("internal_data"))
        menu.addAction("Image Scraping", lambda: self.goto_other_program("image_scraping"))
        menu.addAction("Update Level Model", lambda: self.goto_other_program("external_update"))
        menu.addAction("AI Classification", lambda: self.goto_other_program("ai_classification"))
        menu.addAction("Basket Arrangement", lambda: self.goto_other_program("basket_arrangement"))
        self.context_button.setMenu(menu)
        top_bar.addWidget(self.context_button, alignment=Qt.AlignmentFlag.AlignRight)

        main_layout.addLayout(top_bar)
        main_layout.addSpacing(5)

        # Tạo các tab
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)

        # Tab 1: Import dữ liệu
        import_tab = QWidget()
        import_layout = QVBoxLayout(import_tab)
        import_layout.setContentsMargins(5, 5, 5, 5)
        import_layout.setSpacing(10)
        self.tab_widget.addTab(import_tab, "Import Dữ Liệu")

        # Tab 2: Xử lý dữ liệu
        process_tab = QWidget()
        process_layout = QVBoxLayout(process_tab)
        process_layout.setContentsMargins(5, 5, 5, 5)
        process_layout.setSpacing(10)
        self.tab_widget.addTab(process_tab, "Xử Lý Dữ Liệu")

        # Tab 3: Nhật ký
        log_tab = QWidget()
        log_layout = QVBoxLayout(log_tab)
        log_layout.setContentsMargins(5, 5, 5, 5)
        self.tab_widget.addTab(log_tab, "Nhật Ký")

        # --- Nội dung tab Import dữ liệu ---

        # Thêm RadioButton cho chế độ Import ở trên cùng
        mode_layout = QHBoxLayout()
        mode_label = QLabel("Chế độ Import:")
        mode_label.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
        mode_layout.addWidget(mode_label)

        self.copy_mode_radio = QRadioButton("Tạo Mới")
        self.copy_mode_radio.setChecked(True)  # Mặc định là chế độ Copy
        self.append_mode_radio = QRadioButton("Bổ Sung")
        mode_layout.addWidget(self.copy_mode_radio)
        mode_layout.addWidget(self.append_mode_radio)
        mode_layout.addStretch(1)

        import_layout.addLayout(mode_layout)

        # Thêm tooltip giải thích về chế độ
        tooltip_text = "Tạo Mới: Tạo mới hoàn toàn dữ liệu từ sheet nguồn.\nBổ Sung: Bổ sung dữ liệu từ dòng 'More' trong sheet nguồn vào cuối sheet đích."
        self.copy_mode_radio.setToolTip(tooltip_text)
        self.append_mode_radio.setToolTip(tooltip_text)

        # Phần sheet nguồn - GroupBox cho phần nhập URL
        source_input_group = QGroupBox("Spreadsheet Nguồn")
        source_input_layout = QVBoxLayout(source_input_group)
        source_input_layout.setContentsMargins(8, 12, 8, 8)
        source_input_layout.setSpacing(8)

        # Phần nhập URL/ID nguồn
        url_input_layout = QHBoxLayout()
        self.source_url_input = QLineEdit()
        self.source_url_input.setPlaceholderText("Nhập URL hoặc ID Spreadsheet nguồn")
        self.source_url_input.textChanged.connect(self.on_source_url_changed)
        url_input_layout.addWidget(self.source_url_input, 1)

        self.load_sheets_btn = QPushButton("Load Sheet")
        self.load_sheets_btn.clicked.connect(self.load_source_sheets)
        url_input_layout.addWidget(self.load_sheets_btn)

        source_input_layout.addLayout(url_input_layout)
        import_layout.addWidget(source_input_group)

        # Phần danh sách sheet - GroupBox cho grid 3x3
        self.sheet_list_group = QGroupBox("Danh sách Sheet Nguồn")
        self.sheet_list_group.setMinimumHeight(250)  # Đặt chiều cao tối thiểu lớn hơn
        self.sheet_list_group.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Expanding)
        sheet_list_layout = QVBoxLayout(self.sheet_list_group)
        sheet_list_layout.setContentsMargins(8, 12, 8, 8)

        # Tạo grid layout 3x3 cố định
        self.sheet_grid_layout = QGridLayout()
        self.sheet_grid_layout.setHorizontalSpacing(10)
        self.sheet_grid_layout.setVerticalSpacing(5)
        sheet_list_layout.addLayout(self.sheet_grid_layout)
        sheet_list_layout.addStretch(1)  # Thêm stretch để các checkbox luôn nằm ở phía trên

        import_layout.addWidget(self.sheet_list_group)

        # Phần Spreadsheet đích và sheet đích
        target_group = QGroupBox("Spreadsheet Đích")
        target_layout = QFormLayout(target_group)
        target_layout.setContentsMargins(8, 12, 8, 8)
        target_layout.setSpacing(8)
        import_layout.addWidget(target_group)

        # URL/ID đích
        target_id_layout = QHBoxLayout()
        self.target_id_input = QLineEdit()
        self.target_id_input.setPlaceholderText("Nhập URL hoặc ID Spreadsheet đích (để trống = dùng Spreadsheet nguồn)")
        self.target_id_input.textChanged.connect(self.on_target_url_changed)
        target_id_layout.addWidget(self.target_id_input)

        # Nút để tải danh sách sheet
        self.load_target_sheets_btn = QPushButton("Load")
        self.load_target_sheets_btn.clicked.connect(self.load_target_sheets)
        self.load_target_sheets_btn.setMaximumWidth(60)
        target_id_layout.addWidget(self.load_target_sheets_btn)

        target_layout.addRow("URL/ID đích:", target_id_layout)

        # Thêm combo box để chọn sheet đích
        self.target_sheet_combo = QComboBox()
        self.target_sheet_combo.setEditable(True)  # Cho phép nhập tên mới
        self.target_sheet_combo.setInsertPolicy(QComboBox.InsertPolicy.NoInsert)
        target_layout.addRow("Sheet đích:", self.target_sheet_combo)

        # Nút bắt đầu import
        import_layout.addStretch(1)
        self.import_btn = QPushButton("Bắt Đầu Import Dữ Liệu")
        self.import_btn.setMinimumHeight(36)
        import_layout.addWidget(self.import_btn)
        self.import_btn.clicked.connect(self.start_import)

        # --- Nội dung tab Xử Lý Dữ Liệu ---
        process_intro_label = QLabel("Xử lý dữ liệu đã import để chuẩn hóa các giá trị")
        process_layout.addWidget(process_intro_label)

        # Phần nhập thông tin spreadsheet cần xử lý
        process_spreadsheet_group = QGroupBox("Thông tin Spreadsheet")
        process_spreadsheet_layout = QFormLayout(process_spreadsheet_group)

        # URL/ID của spreadsheet cần xử lý
        self.process_spreadsheet_input = QLineEdit()
        self.process_spreadsheet_input.setPlaceholderText("Nhập URL hoặc ID Spreadsheet cần xử lý")
        process_spreadsheet_layout.addRow("URL/ID:", self.process_spreadsheet_input)

        # Dropdown chọn sheet cần xử lý và nút tải cùng dòng
        sheet_layout = QHBoxLayout()
        self.process_sheet_combo = QComboBox()
        self.process_sheet_combo.setEditable(True)
        self.process_sheet_combo.setInsertPolicy(QComboBox.InsertPolicy.NoInsert)
        sheet_layout.addWidget(self.process_sheet_combo)

        self.load_process_sheets_btn = QPushButton("Load Sheet")
        self.load_process_sheets_btn.clicked.connect(self.load_process_sheets_and_headers)
        self.load_process_sheets_btn.setMaximumWidth(80)
        sheet_layout.addWidget(self.load_process_sheets_btn)

        process_spreadsheet_layout.addRow("Sheet:", sheet_layout)

        process_layout.addWidget(process_spreadsheet_group)

        # Các quy tắc xử lý dữ liệu - Thiết kế mới
        process_rules_group = QGroupBox("Quy tắc xử lý")
        process_rules_layout = QVBoxLayout(process_rules_group)
        process_rules_layout.setContentsMargins(10, 15, 10, 10)
        process_rules_layout.setSpacing(10)

        # Container cho các rules với scroll area có kích thước cố định
        self.rules_container = QWidget()
        self.rules_layout = QVBoxLayout(self.rules_container)
        self.rules_layout.setContentsMargins(0, 0, 0, 0)
        self.rules_layout.setSpacing(2)  # Giảm spacing để các dòng gần nhau hơn

        # Scroll area với kích thước cố định
        rules_scroll_area = QScrollArea()
        rules_scroll_area.setWidgetResizable(True)
        rules_scroll_area.setWidget(self.rules_container)
        rules_scroll_area.setFixedHeight(250)  # Kích thước cố định
        rules_scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        rules_scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        process_rules_layout.addWidget(rules_scroll_area)

        # Khởi tạo danh sách rules và headers
        self.processing_rules = []
        self.available_headers = []

        # Các loại điều kiện xử lý có sẵn
        self.condition_types = [
            "Clear 'X' Values",
            "Format Numbers",
            "Format Percentages",
            "Fix Empty Rows"
        ]

        # Tạo rule mặc định - luôn có 1 dòng trống sẵn có
        self.create_default_rule()

        # Tạo nút + động
        self.create_add_rule_button()

        # Thêm stretch để đẩy các rules lên trên
        self.rules_layout.addStretch()

        process_layout.addWidget(process_rules_group)

        # Nút bắt đầu xử lý
        process_layout.addStretch(1)
        self.process_data_btn = QPushButton("Bắt Đầu Xử Lý Dữ Liệu")
        self.process_data_btn.setMinimumHeight(36)
        self.process_data_btn.clicked.connect(self.start_processing)
        process_layout.addWidget(self.process_data_btn)

        # --- Nội dung tab Nhật ký ---
        # Text edit để hiển thị nhật ký
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)

        # Hiển thị thông báo khởi động
        self.log("Ứng dụng đã khởi động")

    def on_source_url_changed(self, text):
        """Tự động parse ID từ URL của spreadsheet nguồn"""
        if not text.strip():
            return

        sheet_id = extract_sheet_id_from_url(text)
        # Nếu có ID được parse ra và khác với text đã nhập
        if sheet_id and sheet_id != text:
            # Cập nhật text trong input box
            self.source_url_input.blockSignals(True)  # Tạm dừng signals để tránh đệ quy
            self.source_url_input.setText(sheet_id)
            self.source_url_input.blockSignals(False)
            self.source_spreadsheet_id = sheet_id
            self.log(f"Đã parse ID nguồn: {sheet_id} từ URL")
        elif sheet_id:
            # Nếu text đã nhập là ID hoặc đã được parse trước đó
            self.source_spreadsheet_id = sheet_id

    def load_source_sheets(self):
        """Tải danh sách sheet từ Spreadsheet nguồn và hiển thị dạng grid layout 3x3"""
        if not hasattr(self, 'source_spreadsheet_id') or not self.source_spreadsheet_id:
            url = self.source_url_input.text().strip()
            self.source_spreadsheet_id = extract_sheet_id_from_url(url)

        if not self.source_spreadsheet_id:
            QMessageBox.warning(self, "Cảnh Báo", "Vui lòng nhập URL hoặc ID Spreadsheet nguồn hợp lệ")
            return

        try:
            # Xóa checkboxes cũ nếu có
            self.clear_sheet_grid()

            # Lấy danh sách sheet
            self.log(f"Đang tải sheets từ Spreadsheet ID: {self.source_spreadsheet_id}")
            sheet_list = self.raw_data_core.get_sheet_list(self.source_spreadsheet_id)

            if sheet_list:
                # Tạo grid các checkbox cho việc chọn sheet
                cols = 3  # Số cột
                rows = (len(sheet_list) + cols - 1) // cols  # Số dòng cần thiết (làm tròn lên)
                self.sheet_checkboxes = []

                # Đặt kích thước cố định cho grid
                for row in range(rows):
                    # Đảm bảo grid có chiều cao đồng đều
                    self.sheet_grid_layout.setRowMinimumHeight(row, 30)

                # Thêm checkbox cho mỗi sheet
                for i, sheet_name in enumerate(sheet_list):
                    row = i // cols
                    col = i % cols

                    checkbox = QCheckBox(sheet_name)
                    self.sheet_checkboxes.append(checkbox)
                    self.sheet_grid_layout.addWidget(checkbox, row, col)

                self.log(f"Đã tải {len(sheet_list)} sheets")

                # Nếu có sheet đích trống, tự động điền ID nguồn vào đích
                if not self.target_id_input.text().strip():
                    self.target_id_input.setText(self.source_spreadsheet_id)
                    self.load_target_sheets()
            else:
                self.log("Không tìm thấy sheet nào trong spreadsheet này")

        except Exception as e:
            self.log(f"Lỗi khi tải sheets: {str(e)}")
            QMessageBox.critical(self, "Lỗi", f"Không thể tải danh sách sheet: {str(e)}")

    def clear_sheet_grid(self):
        """Xóa tất cả checkbox cũ trong grid"""
        if hasattr(self, 'sheet_checkboxes'):
            for checkbox in self.sheet_checkboxes:
                checkbox.setParent(None)
            self.sheet_checkboxes = []

        # Xóa tất cả widget trong grid layout
        while self.sheet_grid_layout.count():
            item = self.sheet_grid_layout.takeAt(0)
            widget = item.widget()
            if widget is not None:
                widget.deleteLater()

    def get_selected_sheets(self):
        """Lấy danh sách các sheet đã chọn"""
        selected_sheets = []

        if hasattr(self, 'sheet_checkboxes') and hasattr(self, 'source_spreadsheet_id'):
            for checkbox in self.sheet_checkboxes:
                if checkbox.isChecked():
                    selected_sheets.append({
                        'spreadsheet_id': self.source_spreadsheet_id,
                        'sheet_name': checkbox.text(),
                    })

        return selected_sheets

    def on_target_url_changed(self, text):
        """Tự động parse ID từ URL của spreadsheet đích"""
        if not text.strip():
            return

        sheet_id = extract_sheet_id_from_url(text)
        # Nếu có ID được parse ra và khác với text đã nhập
        if sheet_id and sheet_id != text:
            # Cập nhật text trong input box
            self.target_id_input.blockSignals(True)  # Tạm dừng signals để tránh đệ quy
            self.target_id_input.setText(sheet_id)
            self.target_id_input.blockSignals(False)
            self.log(f"Đã parse ID đích: {sheet_id} từ URL")

    def load_target_sheets(self):
        """Tải danh sách sheet từ spreadsheet đích"""
        target_url = self.target_id_input.text().strip()
        if not target_url:
            # Nếu trống, thử sử dụng ID nguồn
            if hasattr(self, 'source_spreadsheet_id'):
                target_url = self.source_spreadsheet_id
                self.target_id_input.setText(target_url)
                self.log(f"Sử dụng Spreadsheet nguồn làm đích: {target_url}")
            else:
                QMessageBox.warning(self, "Cảnh Báo", "Vui lòng nhập URL hoặc ID Spreadsheet đích, hoặc tải sheet nguồn trước")
                return

        # Parse spreadsheet ID từ URL nếu cần
        target_id = extract_sheet_id_from_url(target_url)

        try:
            self.log(f"Đang tải danh sách sheet từ Spreadsheet ID đích: {target_id}")

            # Lấy danh sách sheet từ raw_data_core
            sheet_list = self.raw_data_core.get_sheet_list(target_id)

            # Xóa danh sách sheet hiện tại
            self.target_sheet_combo.clear()

            # Thêm một item trống để không chọn sheet nào mặc định
            self.target_sheet_combo.addItem("")

            # Thêm vào danh sách mới
            if sheet_list:
                for sheet_name in sheet_list:
                    self.target_sheet_combo.addItem(sheet_name)

                # Nếu có sheet tên "Pool Deal", chọn nó
                pool_deal_index = self.target_sheet_combo.findText("Pool Deal")
                if pool_deal_index >= 0:
                    self.target_sheet_combo.setCurrentIndex(pool_deal_index)
                    self.log(f"Đã tự động chọn sheet 'Pool Deal'")
                else:
                    # Mặc định không chọn sheet nào, để trống
                    self.target_sheet_combo.setCurrentIndex(0)

                self.log(f"Đã tải {len(sheet_list)} sheet từ spreadsheet đích")
            else:
                self.log("Không tìm thấy sheet nào trong spreadsheet đích này")

        except Exception as e:
            self.log(f"Lỗi khi tải danh sách sheet đích: {str(e)}")
            QMessageBox.critical(self, "Lỗi", f"Không thể tải danh sách sheet đích: {str(e)}")

    def log(self, message):
        """Thêm thông báo vào nhật ký"""
        self.log_text.append(message)

    def show_loading_dialog(self, title="Đang xử lý...", status="Đang khởi tạo..."):
        """Hiển thị dialog loading"""
        self.loading_dialog = LoadingDialog(self, title)
        self.loading_dialog.set_status(status)
        self.loading_dialog.start_animation()
        self.loading_dialog.show()
        QApplication.processEvents()  # Đảm bảo dialog được hiển thị

    def update_loading_status(self, status):
        """Cập nhật trạng thái loading"""
        if hasattr(self, 'loading_dialog') and self.loading_dialog:
            self.loading_dialog.set_status(status)
            QApplication.processEvents()

    def hide_loading_dialog(self):
        """Ẩn dialog loading"""
        if hasattr(self, 'loading_dialog') and self.loading_dialog:
            self.loading_dialog.stop_animation()
            self.loading_dialog.close()
            self.loading_dialog = None

    def start_worker_thread(self, task_type, *args, **kwargs):
        """Bắt đầu worker thread cho tác vụ nặng"""
        # Dừng worker thread cũ nếu đang chạy
        if self.worker_thread and self.worker_thread.isRunning():
            self.worker_thread.quit()
            self.worker_thread.wait()

        # Tạo worker thread mới
        self.worker_thread = WorkerThread(task_type, *args, **kwargs)
        self.worker_thread.set_raw_data_core(self.raw_data_core)

        # Kết nối signals
        self.worker_thread.progress_update.connect(self.update_loading_status)
        self.worker_thread.finished_signal.connect(self.on_worker_finished)
        self.worker_thread.error_signal.connect(self.on_worker_error)

        # Bắt đầu thread
        self.worker_thread.start()

    def on_worker_finished(self, success, result):
        """Xử lý khi worker thread hoàn thành"""
        self.hide_loading_dialog()

        if hasattr(self, 'current_task_type'):
            if self.current_task_type == "import":
                self.handle_import_result(success, result)
            elif self.current_task_type == "process":
                self.handle_process_result(success, result)
            elif self.current_task_type == "load_sheets":
                self.handle_load_sheets_result(success, result)

    def on_worker_error(self, error_message):
        """Xử lý khi worker thread gặp lỗi"""
        self.hide_loading_dialog()
        self.log(f"Lỗi: {error_message}")
        QMessageBox.critical(self, "Lỗi", f"Đã xảy ra lỗi: {error_message}")

    def load_process_sheets_and_headers(self):
        """Tải danh sách sheet từ spreadsheet cần xử lý và tự động load headers"""
        spreadsheet_url = self.process_spreadsheet_input.text().strip()
        if not spreadsheet_url:
            # Nếu trống, thử sử dụng ID đã nhập ở tab Import
            if hasattr(self, 'target_id_input') and self.target_id_input.text().strip():
                spreadsheet_url = self.target_id_input.text().strip()
                self.process_spreadsheet_input.setText(spreadsheet_url)
                self.log(f"Sử dụng Spreadsheet đích từ tab Import: {spreadsheet_url}")
            else:
                QMessageBox.warning(self, "Cảnh Báo", "Vui lòng nhập URL hoặc ID Spreadsheet cần xử lý")
                return

        # Parse spreadsheet ID từ URL
        spreadsheet_id = extract_sheet_id_from_url(spreadsheet_url)

        # Hiển thị loading dialog
        self.show_loading_dialog("Đang Tải Sheet", "Đang tải danh sách sheet...")

        self.log(f"Đang tải danh sách sheet từ Spreadsheet ID: {spreadsheet_id}")

        # Lưu task type để xử lý kết quả
        self.current_task_type = "load_sheets"

        # Tự động chọn sheet "Pool Deal" nếu có
        selected_sheet = "Pool Deal"

        # Bắt đầu worker thread
        self.start_worker_thread("load_sheets", spreadsheet_id, selected_sheet)

    def handle_load_sheets_result(self, success, result):
        """Xử lý kết quả load sheets"""
        if not success or not result:
            self.log("Không tìm thấy sheet nào trong spreadsheet này")
            return

        sheet_list = result.get('sheet_list', [])
        headers = result.get('headers', [])

        # Xóa danh sách sheet hiện tại
        self.process_sheet_combo.clear()

        # Thêm vào danh sách mới
        if sheet_list:
            for sheet_name in sheet_list:
                self.process_sheet_combo.addItem(sheet_name)

            # Nếu có sheet tên "Pool Deal", chọn nó
            pool_deal_index = self.process_sheet_combo.findText("Pool Deal")
            if pool_deal_index >= 0:
                self.process_sheet_combo.setCurrentIndex(pool_deal_index)
                self.log(f"Đã tự động chọn sheet 'Pool Deal'")
            else:
                # Chọn sheet đầu tiên
                self.process_sheet_combo.setCurrentIndex(0)

            self.log(f"Đã tải {len(sheet_list)} sheet từ spreadsheet")

            # Xử lý headers nếu có
            if headers:
                # Bỏ qua 3 header đầu tiên
                all_headers = headers[3:] if len(headers) > 3 else headers

                # Danh sách các headers cần loại bỏ khỏi giao diện chọn
                excluded_headers = [
                    "Cluster",
                    "L1 Cat",
                    "Nhà bán hàng đồng ý cho BEYONDK mượn sản phẩm chính + sản phẩm quà để KOL review trong Livestream?",
                    "KOL có được sử dụng sản phẩm Nhà bán hàng cho mượn trong quá trình diễn ra Livestream với mục đích review sản phẩm này không?",
                    "Link hình ảnh sản phẩm (hình sp chính & hình quà chính xác)",
                    "(Đối với ngành FMCG) Ngày hết hạn sử dụng của sản phẩm trong phiên live (nếu có)",
                    "Cho phép Shopee, Beyond-K, Diệp Lê và các KOL tham gia phiên Livestream được quyền công bố giá sau cùng*trên các nền tảng mạng xã hội cho mục đích truyền thông về phiên Livestream?\n*Giá sau cùng: là giá sản phẩm sau khi đã áp dụng: mã giảm giá của Nhà Bán Hàng (nếu có), trợ giá của Shopee (nếu có) và tất cả mã giảm giá của từ Shopee.",
                    "Cho phép Shopee, Beyond-K, Diệp Lê và các KOL tham gia phiên Livestream được sử dụng hình ảnh/thông tin sản phẩm vào trong teaser video/teaser post cho mục đích truyền thông về phiên Livestream ?",
                    "Brand note (nếu cần)",
                    "Brand note",
                    "brand note (nếu cần)",
                    "brand note",
                    "BRAND NOTE (NẾU CẦN)",
                    "BRAND NOTE",
                    "Cho phép Shopee, Beyond-K, Diệp Lê và các KOL tham gia phiên Livestream được quyền công bố giá sau cùng*trên các nền tảng mạng xã hội cho mục đích truyền thông về phiên Livestream?\n\n*Giá sau cùng: là giá sản phẩm sau khi đã áp dụng: mã giảm giá của Nhà Bán Hàng (nếu có), trợ giá của Shopee (nếu có) và tất cả mã giảm giá của từ Shopee."
                ]

                # Lọc bỏ các headers không mong muốn khỏi danh sách hiển thị
                self.available_headers = []
                for header in all_headers:
                    # Kiểm tra xem header có trong danh sách loại trừ không
                    should_exclude = False
                    header_clean = header.strip().lower() if header else ""

                    for excluded in excluded_headers:
                        excluded_clean = excluded.strip().lower() if excluded else ""

                        # So sánh chính xác
                        if header_clean == excluded_clean:
                            should_exclude = True
                            break

                        # Kiểm tra các trường hợp đặc biệt cho Brand note
                        if "brand note" in header_clean and "brand note" in excluded_clean:
                            should_exclude = True
                            break

                    # Kiểm tra riêng cho tất cả các biến thể của Brand note
                    if "brand note" in header_clean:
                        should_exclude = True

                    if not should_exclude:
                        self.available_headers.append(header)

                excluded_count = len(all_headers) - len(self.available_headers)
                self.log(f"Đã tải {len(self.available_headers)} headers (bỏ qua 3 header đầu + {excluded_count} headers không cần thiết)")

                # Enable nút + và các rule
                self.add_rule_btn.setEnabled(True)

                # Enable rule mặc định nếu có
                if self.processing_rules:
                    for rule_widget in self.processing_rules:
                        rule_widget.setEnabled(True)
            else:
                self.log("Không tìm thấy headers trong sheet này")
        else:
            self.log("Không tìm thấy sheet nào trong spreadsheet này")

    def start_processing(self):
        """Bắt đầu xử lý dữ liệu (Bước 2) với rules đã chọn"""
        try:
            # Lấy Spreadsheet ID
            spreadsheet_url = self.process_spreadsheet_input.text().strip()
            if not spreadsheet_url:
                QMessageBox.warning(self, "Cảnh Báo", "Vui lòng nhập URL hoặc ID Spreadsheet cần xử lý")
                return

            # Parse ID từ URL
            spreadsheet_id = extract_sheet_id_from_url(spreadsheet_url)

            # Lấy tên sheet
            sheet_name = self.process_sheet_combo.currentText().strip()
            if not sheet_name:
                QMessageBox.warning(self, "Cảnh Báo", "Vui lòng chọn sheet cần xử lý")
                return

            # Kiểm tra xem có rules nào được thiết lập không
            if not self.processing_rules:
                QMessageBox.warning(self, "Cảnh Báo", "Vui lòng thêm ít nhất một quy tắc xử lý")
                return

            # Kiểm tra xem mỗi rule có headers được chọn không
            invalid_rules = []
            for idx, rule_widget in enumerate(self.processing_rules):
                if not rule_widget.selected_headers:
                    condition_name = rule_widget.condition_combo.currentText()
                    invalid_rules.append(f"Quy tắc {idx+1}: {condition_name}")

            if invalid_rules:
                error_msg = "Các quy tắc sau chưa có headers được chọn:\n" + "\n".join(invalid_rules)
                QMessageBox.warning(self, "Quy tắc không hợp lệ", error_msg)
                return

            # Tạo danh sách rules để truyền cho raw_data_core
            rules_to_apply = []
            for rule_widget in self.processing_rules:
                if rule_widget.selected_headers:  # Chỉ thêm rules có headers được chọn
                    rule_info = {
                        'condition': rule_widget.condition_combo.currentText(),
                        'headers': rule_widget.selected_headers.copy()
                    }
                    rules_to_apply.append(rule_info)

            # Hiển thị loading dialog
            self.show_loading_dialog("Đang Xử Lý Dữ Liệu", f"Chuẩn bị xử lý {len(rules_to_apply)} quy tắc...")

            # Log thông tin
            self.log(f"Bắt đầu xử lý dữ liệu trên sheet '{sheet_name}' trong Spreadsheet {spreadsheet_id}")
            self.log(f"Sẽ áp dụng {len(rules_to_apply)} quy tắc xử lý")

            for rule_info in rules_to_apply:
                self.log(f"- {rule_info['condition']}: {len(rule_info['headers'])} headers")

            # Lưu task type để xử lý kết quả
            self.current_task_type = "process"

            # Bắt đầu worker thread
            self.start_worker_thread("process", spreadsheet_id, sheet_name, rules_to_apply)

        except Exception as e:
            self.log(f"Lỗi khi chuẩn bị xử lý dữ liệu: {str(e)}")
            QMessageBox.critical(self, "Lỗi", f"Đã xảy ra lỗi: {str(e)}")

    def handle_import_result(self, success, result):
        """Xử lý kết quả import"""
        if result:
            completion_message = "Đã import dữ liệu thành công!"

            self.log(completion_message)
            QMessageBox.information(self, "Thành Công", completion_message)

            # Cập nhật thông tin cho tab Xử lý dữ liệu
            self.process_spreadsheet_input.setText(self.import_target_spreadsheet_id)
            self.load_process_sheets_and_headers()

            # Chuyển sang tab Xử lý dữ liệu
            self.tab_widget.setCurrentIndex(1)
        else:
            self.log("Import dữ liệu thất bại!")
            QMessageBox.critical(self, "Lỗi", "Không thể import dữ liệu. Xem nhật ký để biết thêm chi tiết.")

    def handle_process_result(self, success, result):
        """Xử lý kết quả processing"""
        if result:
            self.log("Xử lý dữ liệu thành công!")
            QMessageBox.information(self, "Thành Công", "Đã xử lý dữ liệu thành công!")

            # Reset chỉ rules sau khi xử lý thành công (giữ nguyên sheet và headers)
            self.reset_processing_rules_only()
        else:
            self.log("Xử lý dữ liệu thất bại!")
            QMessageBox.critical(self, "Lỗi", "Không thể xử lý dữ liệu. Xem nhật ký để biết thêm chi tiết.")

    def reset_processing_rules(self):
        """Reset lại tất cả processing rules - xóa tất cả và tạo lại 1 dòng mặc định"""
        # Xóa tất cả rules hiện tại
        for rule_widget in self.processing_rules:
            self.rules_layout.removeWidget(rule_widget)
            rule_widget.deleteLater()

        # Xóa danh sách rules
        self.processing_rules.clear()

        # Xóa tất cả widget trong layout (bao gồm nút + và stretch)
        while self.rules_layout.count() > 0:
            item = self.rules_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()

        # Tạo lại rule mặc định duy nhất
        self.create_default_rule()

        # Tạo lại nút + (sẽ nằm bên dưới rule mặc định)
        self.create_add_rule_button()

        # Thêm lại stretch
        self.rules_layout.addStretch()

        # Cập nhật container
        self.rules_container.updateGeometry()

        self.log("Đã reset tất cả quy tắc xử lý - chỉ còn 1 dòng mặc định")

    def reset_processing_rules_only(self):
        """Reset chỉ processing rules mà không ảnh hưởng đến sheet và headers đã load"""
        # Xóa tất cả rules hiện tại
        for rule_widget in self.processing_rules:
            self.rules_layout.removeWidget(rule_widget)
            rule_widget.deleteLater()

        # Xóa danh sách rules
        self.processing_rules.clear()

        # Xóa tất cả widget trong layout (bao gồm nút + và stretch)
        while self.rules_layout.count() > 0:
            item = self.rules_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()

        # Tạo lại rule mặc định duy nhất
        self.create_default_rule()

        # Tạo lại nút + (sẽ nằm bên dưới rule mặc định)
        self.create_add_rule_button()

        # Thêm lại stretch
        self.rules_layout.addStretch()

        # Cập nhật container
        self.rules_container.updateGeometry()

        # Giữ nguyên available_headers và sheet combo - không reset
        self.log("Đã reset quy tắc xử lý - giữ nguyên sheet và headers đã load")

    @pyqtSlot()
    def start_import(self):
        """Bắt đầu quá trình import dữ liệu"""
        try:
            # Debug: Kiểm tra phương thức tồn tại
            if not hasattr(self, 'load_process_sheets_and_headers'):
                self.log("❌ CẢNH BÁO: Phương thức load_process_sheets_and_headers không tồn tại!")
                return

            # Lấy danh sách sheet nguồn đã chọn
            source_data = self.get_selected_sheets()
            if not source_data:
                QMessageBox.warning(self, "Cảnh Báo", "Vui lòng chọn ít nhất một sheet nguồn")
                return

            # Lấy Spreadsheet ID đích
            target_url = self.target_id_input.text().strip()
            if not target_url:
                # Nếu trống, sử dụng ID nguồn
                if hasattr(self, 'source_spreadsheet_id'):
                    target_url = self.source_spreadsheet_id
                    self.log(f"Sử dụng Spreadsheet nguồn làm đích: {target_url}")
                else:
                    QMessageBox.warning(self, "Cảnh Báo", "Vui lòng nhập URL hoặc ID Spreadsheet đích, hoặc tải sheet nguồn trước")
                    return

            # Parse ID từ URL
            target_spreadsheet_id = extract_sheet_id_from_url(target_url)

            # Lấy tên sheet đích (nếu có)
            target_sheet = self.target_sheet_combo.currentText().strip()

            # Nếu không chọn sheet nào hoặc để trống, sử dụng sheet Pool Deal mặc định
            if not target_sheet:
                target_sheet = "Pool Deal"
                self.log(f"Không có sheet đích được chọn. Sẽ tạo sheet '{target_sheet}' từ template.")
            else:
                self.log(f"Sử dụng sheet đích đã chọn: '{target_sheet}'")

            # Xác định chế độ import từ RadioButton
            mode = "append" if self.append_mode_radio.isChecked() else "copy"

            # Hiển thị loading dialog
            self.show_loading_dialog("Data Processing", f"Chuẩn bị import {len(source_data)} sheet...")

            # Log thông tin
            self.log(f"Bắt đầu import dữ liệu vào Sheet '{target_sheet}' trong Spreadsheet {target_spreadsheet_id}")
            self.log(f"Chế độ import: {mode}")

            # Lưu thông tin để xử lý sau khi hoàn thành
            self.current_task_type = "import"
            self.import_target_spreadsheet_id = target_spreadsheet_id
            self.import_target_sheet = target_sheet

            # Bắt đầu worker thread
            self.start_worker_thread(
                "import",
                target_spreadsheet_id,
                source_data,
                target_sheet,
                mode,
                normalize_header_func=normalize_header
            )

        except Exception as e:
            self.log(f"Lỗi khi chuẩn bị import dữ liệu: {str(e)}")
            QMessageBox.critical(self, "Lỗi", f"Đã xảy ra lỗi: {str(e)}")

    def create_default_rule(self):
        """Tạo rule mặc định"""
        default_rule = self.create_rule_widget()
        default_rule.setEnabled(False)  # Disable cho đến khi load sheet và headers
        self.rules_layout.addWidget(default_rule)
        self.processing_rules.append(default_rule)

    def create_add_rule_button(self):
        """Tạo nút + để thêm rule mới"""
        self.add_rule_btn = QPushButton("+")
        self.add_rule_btn.setFixedSize(20, 20)  # Giống kích thước nút X
        self.add_rule_btn.setEnabled(False)  # Disable cho đến khi load sheet và headers
        self.add_rule_btn.clicked.connect(self.add_processing_rule)
        # Không set style để giống với nút X (sử dụng style mặc định)

        # Thêm nút vào layout (sẽ được di chuyển khi có rule mới)
        self.rules_layout.addWidget(self.add_rule_btn)

    def create_rule_widget(self):
        """Tạo widget cho một rule mới"""
        rule_widget = QWidget()
        rule_layout = QHBoxLayout(rule_widget)
        rule_layout.setContentsMargins(5, 2, 5, 2)  # Giảm margin trên/dưới
        rule_layout.setSpacing(10)

        # Đặt chiều cao cố định cho widget để các dòng gần nhau hơn
        rule_widget.setFixedHeight(35)

        # Dropdown để chọn loại điều kiện
        condition_combo = QComboBox()
        for condition in self.condition_types:
            condition_combo.addItem(condition)
        condition_combo.setFixedWidth(200)
        rule_layout.addWidget(condition_combo)

        # Nút để chọn headers
        headers_btn = QPushButton("Chọn Headers")
        headers_btn.setFixedWidth(120)
        headers_btn.clicked.connect(lambda: self.select_headers_for_rule(rule_widget))
        rule_layout.addWidget(headers_btn)

        # Label hiển thị số lượng headers đã chọn
        headers_count_label = QLabel("0 headers được chọn")
        headers_count_label.setMinimumWidth(150)
        rule_layout.addWidget(headers_count_label)

        # Nút xóa rule
        remove_btn = QPushButton("✕")
        remove_btn.setFixedWidth(30)
        remove_btn.setFixedHeight(30)
        remove_btn.clicked.connect(lambda: self.remove_rule(rule_widget))
        rule_layout.addWidget(remove_btn)

        # Thêm stretch để đẩy các elements sang trái
        rule_layout.addStretch()

        # Lưu các control vào widget để sử dụng sau này
        rule_widget.condition_combo = condition_combo
        rule_widget.headers_btn = headers_btn
        rule_widget.headers_count_label = headers_count_label
        rule_widget.remove_btn = remove_btn
        rule_widget.selected_headers = []  # Danh sách headers đã chọn

        return rule_widget

    def add_processing_rule(self):
        """Thêm một rule mới"""
        if not self.available_headers:
            QMessageBox.warning(self, "Cảnh Báo", "Vui lòng tải sheet trước để có headers")
            return

        # Tạo rule mới
        new_rule = self.create_rule_widget()

        # Tìm vị trí của nút + hiện tại
        add_btn_index = self.rules_layout.indexOf(self.add_rule_btn)

        # Chèn rule mới trước nút +
        self.rules_layout.insertWidget(add_btn_index, new_rule)
        self.processing_rules.append(new_rule)

        # Cập nhật kích thước container
        self.rules_container.updateGeometry()

    def select_headers_for_rule(self, rule_widget):
        """Mở dialog để chọn headers cho rule"""
        if not self.available_headers:
            QMessageBox.warning(self, "Cảnh Báo", "Vui lòng tải sheet trước để có headers")
            return

        dialog = QDialog(self)
        dialog.setWindowTitle("Chọn Headers")
        dialog.setFixedSize(450, 500)
        dialog.setModal(True)

        # Layout chính
        main_layout = QVBoxLayout(dialog)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(10)

        # Label hướng dẫn
        desc_label = QLabel("Chọn headers để áp dụng điều kiện này:")
        main_layout.addWidget(desc_label)

        # Thông tin số lượng đã chọn
        self.selected_count_label = QLabel(f"Đã chọn: {len(rule_widget.selected_headers)} headers")
        main_layout.addWidget(self.selected_count_label)

        # Scroll area để chứa các checkbox
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)

        # Widget chứa các checkbox
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        scroll_layout.setContentsMargins(5, 5, 5, 5)
        scroll_layout.setSpacing(5)

        # Tạo các checkbox cho từng header
        checkboxes = {}
        for header in self.available_headers:
            checkbox = QCheckBox(header)

            # Nếu header đã được chọn trước đó, check nó
            if header in rule_widget.selected_headers:
                checkbox.setChecked(True)

            # Kết nối signal để cập nhật số lượng đã chọn
            checkbox.stateChanged.connect(lambda: self.update_selected_count(checkboxes))

            scroll_layout.addWidget(checkbox)
            checkboxes[header] = checkbox

        # Thêm stretch để đẩy các checkbox lên trên
        scroll_layout.addStretch()

        scroll_area.setWidget(scroll_widget)
        main_layout.addWidget(scroll_area)

        # Nút chọn tất cả / bỏ chọn tất cả
        select_all_layout = QHBoxLayout()
        select_all_btn = QPushButton("Chọn Tất Cả")
        deselect_all_btn = QPushButton("Bỏ Chọn Tất Cả")

        select_all_btn.clicked.connect(lambda: self.toggle_all_checkboxes(checkboxes, True))
        deselect_all_btn.clicked.connect(lambda: self.toggle_all_checkboxes(checkboxes, False))

        select_all_layout.addWidget(select_all_btn)
        select_all_layout.addWidget(deselect_all_btn)
        select_all_layout.addStretch()
        main_layout.addLayout(select_all_layout)

        # Nút OK và Cancel
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        cancel_btn = QPushButton("Hủy")
        ok_btn = QPushButton("OK")

        ok_btn.setAutoDefault(True)
        ok_btn.setDefault(True)

        cancel_btn.clicked.connect(dialog.reject)
        ok_btn.clicked.connect(dialog.accept)

        button_layout.addWidget(cancel_btn)
        button_layout.addWidget(ok_btn)
        main_layout.addLayout(button_layout)

        # Lưu reference để sử dụng trong callback
        self.current_checkboxes = checkboxes

        # Hiển thị dialog và xử lý kết quả
        if dialog.exec() == QDialog.DialogCode.Accepted:
            selected = []
            for header, checkbox in checkboxes.items():
                if checkbox.isChecked():
                    selected.append(header)

            # Cập nhật danh sách headers đã chọn của rule
            rule_widget.selected_headers = selected
            count = len(selected)
            rule_widget.headers_count_label.setText(f"{count} headers được chọn")

    def update_selected_count(self, checkboxes):
        """Cập nhật số lượng headers đã chọn"""
        if hasattr(self, 'selected_count_label'):
            count = sum(1 for cb in checkboxes.values() if cb.isChecked())
            self.selected_count_label.setText(f"Đã chọn: {count} headers")

    def toggle_all_checkboxes(self, checkboxes, checked):
        """Chọn tất cả hoặc bỏ chọn tất cả checkboxes"""
        for checkbox in checkboxes.values():
            checkbox.setChecked(checked)
        self.update_selected_count(checkboxes)

    def remove_rule(self, rule_widget):
        """Xóa một rule"""
        # Không cho phép xóa nếu chỉ còn 1 rule
        if len(self.processing_rules) <= 1:
            QMessageBox.information(self, "Không thể xóa", "Phải có ít nhất một quy tắc.")
            return

        # Xóa widget khỏi giao diện
        self.rules_layout.removeWidget(rule_widget)
        rule_widget.deleteLater()

        # Xóa khỏi danh sách
        if rule_widget in self.processing_rules:
            self.processing_rules.remove(rule_widget)

    def handle_back(self):
        """Xử lý khi người dùng nhấn nút Back"""
        # Xử lý nếu đang trong quá trình thực hiện tác vụ
        if hasattr(self, 'worker_thread') and self.worker_thread and self.worker_thread.isRunning():
            reply = QMessageBox.question(
                self,
                "Xác nhận",
                "Đang có tiến trình xử lý dữ liệu đang chạy. Quay về màn hình chính sẽ dừng xử lý.\n\nBạn có chắc muốn quay lại không?",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.No:
                return

            # Người dùng đã xác nhận, dừng xử lý
            self.stop_processing()

        # Gọi callback để quay về màn hình chính
        if hasattr(self, 'back_callback') and self.back_callback:
            self.back_callback()

    def on_module_activated(self):
        """Được gọi khi module được kích hoạt từ main.py - reset UI nếu không có processing"""
        # Chỉ reset UI nếu không có tiến trình đang chạy
        if not (hasattr(self, 'worker_thread') and self.worker_thread and self.worker_thread.isRunning()):
            # Reset UI về trạng thái ban đầu
            self.reset_ui_to_initial_state()

    def reset_ui_to_initial_state(self):
        """Reset UI về trạng thái ban đầu"""
        # Reset các tab về tab đầu tiên
        if hasattr(self, 'tab_widget'):
            self.tab_widget.setCurrentIndex(0)

        # Reset các input fields và progress
        if hasattr(self, 'progress_bar'):
            self.progress_bar.setValue(0)
        if hasattr(self, 'status_label'):
            self.status_label.setText("")

        # Reset các table nếu có
        for widget in self.findChildren(QTableWidget):
            widget.setRowCount(0)

    def eventFilter(self, obj, event):
        """Filter các sự kiện của các đối tượng"""
        if obj == self.context_button and event.type() == QEvent.Type.Enter:
            self.context_button.showMenu()
        return super().eventFilter(obj, event)

    def goto_other_program(self, program_name):
        """Chuyển đến chương trình khác"""
        # Kiểm tra nếu đang có tiến trình xử lý
        if hasattr(self, 'worker_thread') and self.worker_thread and self.worker_thread.isRunning():
            QMessageBox.information(
                self, 'Thông báo',
                'Tiến trình xử lý đang chạy trong nền và sẽ tiếp tục cho đến khi hoàn thành.'
            )

        # Sử dụng tên thuộc tính khác để tránh đệ quy
        if hasattr(self, '_goto_program_callback') and callable(self._goto_program_callback):
            self._goto_program_callback(program_name)

    def stop_processing(self):
        """Phương thức dừng xử lý (sử dụng khi đóng ứng dụng)"""
        if hasattr(self, 'worker_thread') and self.worker_thread and self.worker_thread.isRunning():
            self.worker_thread.quit()
            self.worker_thread.wait(1000)  # Đợi tối đa 1 giây
            print("Đã dừng xử lý raw data")

    def keyPressEvent(self, event):
        """Xử lý phím tắt"""
        if event.key() == Qt.Key.Key_Backspace:
            self.handle_back()
        else:
            super().keyPressEvent(event)

    def closeEvent(self, event):
        """Xử lý sự kiện đóng window"""
        if hasattr(self, 'back_callback') and self.back_callback:
            self.back_callback()
            event.ignore()  # Không đóng window mà quay lại màn hình chính

# Hàm chính để chạy ứng dụng
def main():
    app = QApplication(sys.argv)
    window = RawDataUI()
    window.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main()