from gsheet_manager import GoogleSheetManager  # Import module qu<PERSON><PERSON> lý Google Sheets

import pandas as pd
import os, re, tempfile, csv, base64, json, io, sys
from datetime import datetime
from dateutil.parser import parse  # robust date parsing
from gspread_dataframe import set_with_dataframe
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, QFormLayout, QPushButton,
    QFileDialog, QLabel, QMessageBox, QInputDialog, QLineEdit, QComboBox,
    QTextEdit, QApplication, QToolButton, QMenu, QRadioButton
)
from PyQt6.QtCore import Qt, QEvent
from PyQt6.QtGui import QGuiApplication
from google.cloud import storage, bigquery
from google.oauth2.credentials import Credentials
from google.auth.transport.requests import Request
from google_auth_oauthlib.flow import InstalledAppFlow

# OAuth credentials chính cho Import Data
CREDENTIALS_BASE64 = "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

# OAuth credentials thay thế từ các module khác
ALTERNATIVE_CREDENTIALS = [
    # Credentials từ external_update.py
    "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
]

# Cấu hình BigQuery
BQ_PROJECT_ID = "beyondk-live-data"
BQ_BUCKET_NAME = "beyondk-scraped-data"
BQ_DATASET_ID = "scraped_data"
BQ_SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
BQ_CREDENTIALS = "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

# Định nghĩa cấu trúc dữ liệu cho bảng BigQuery
EXPECTED_HEADER = [
    "product_link", "shop_link", "item_name", "brand", "description",
    "created_date", "item_id", "shop_id", "cate_code1", "cate_code2",
    "current_price", "lowest_model_price", "highest_model_price",
    "discount", "stock", "weight", "image_link", "rating_count",
    "view_count", "like_count", "rating", "L30d_order", "L30d_gmv",
    "Lifetime_order", "Lifetime_gmv", "Location"
]

# Định nghĩa kiểu dữ liệu cho các cột
INT_COLUMNS = {
    "item_id", "shop_id", "cate_code1", "cate_code2", "stock", "rating_count",
    "view_count", "like_count", "L30d_order", "Lifetime_order"
}

FLOAT_COLUMNS = {
    "current_price", "lowest_model_price", "highest_model_price", "discount",
    "weight", "rating", "L30d_gmv", "Lifetime_gmv"
}

DATE_COLUMNS = {"created_date"}

# Ánh xạ tiếng Việt
VIETNAMESE_MAPPING = {
    "Link sản phẩm": "product_link",
    "Link Shop": "shop_link",
    "Tên sản phẩm": "item_name",
    "Thương hiệu": "brand",
    "Mô tả": "description",
    "Ngày tạo": "created_date",
    "Mã Sản phẩm": "shop_id",  # swapped
    "Mã Shop": "item_id",      # swapped
    "Chuyên mục": None,        # special handling
    "Giá hiện tại": "current_price",
    "Giá thấp nhất": "lowest_model_price",
    "Giá cao nhất": "highest_model_price",
    "Giảm giá": "discount",
    "Tồn kho": "stock",
    "Cân nặng": "weight",
    "Hình ảnh": "image_link",
    "Số Đánh giá": "rating_count",
    "Số lượt xem": "view_count",
    "Số thích": "like_count",
    "Điểm đánh giá": "rating",
    "Đã bán 30 ngày": "L30d_order",
    "Doanh số 30 ngày": "L30d_gmv",
    "Đã bán toàn thời gian": "Lifetime_order",
    "Doanh số toàn thời gian": "Lifetime_gmv",
    "Vị trí": "Location",
    "Video": None  # drop
}

# Tăng giới hạn kích thước trường CSV để xử lý dữ liệu lớn
try:
    csv.field_size_limit(sys.maxsize)
except OverflowError:
    # Một số hệ thống 32-bit sẽ gặp lỗi với maxsize, dùng giá trị thấp hơn
    csv.field_size_limit(2147483647)  # 2^31-1

class ImportScrapedWidget(QWidget):
    def __init__(self):
        super().__init__()
        # Khởi tạo đối tượng quản lý Google Sheets với hỗ trợ nhiều OAuth credentials
        self.sheet_manager = GoogleSheetManager(
            auth_type='oauth',
            credentials_data=CREDENTIALS_BASE64,
            all_oauth_credentials=ALTERNATIVE_CREDENTIALS
        )
        # Các biến lưu trữ thông tin
        self.import_spreadsheet = None  # Spreadsheet object sau khi load thành công
        self.current_import_file = None  # Đường dẫn file Excel đã chọn
        self.current_import_files = []  # Danh sách đường dẫn file Excel (cho BigQuery)

        # Khởi tạo UI trước
        self.initUI()

        # Di chuyển check_temp_spreadsheet_file() xuống đây, sau khi UI đã được tạo
        # self.check_temp_spreadsheet_file()

    def initUI(self):
        # Create top bar for the Back button
        main_layout = QVBoxLayout(self)
        top_bar = QHBoxLayout()
        btn_back = QPushButton("⬅️ Trở về giao diện chính")
        btn_back.setFixedSize(150, 40)
        btn_back.setStyleSheet("border: none; font-size: 12px; font-weight: bold;")
        btn_back.clicked.connect(self.handle_back)
        top_bar.addWidget(btn_back, alignment=Qt.AlignmentFlag.AlignLeft)

        self.context_button = QToolButton()
        self.context_button.setText("Chuyển đến ➡️")
        self.context_button.setFixedSize(100, 30)
        self.context_button.setStyleSheet("""
            QToolButton {
                border: none;
                background: transparent;
                font-size: 12px;
                font-weight: bold;
            }
            QToolButton::menu-indicator {
                image: none;
                width: 0px;
            }
        """)
        self.context_button.installEventFilter(self)
        self.context_button.setPopupMode(QToolButton.ToolButtonPopupMode.InstantPopup)
        menu = QMenu(self.context_button)
        menu.addAction("Data Scraping", lambda: self.goto_other_program("autoshopee_scraping"))
        menu.addAction("Data Handler", lambda: self.goto_other_program("data_handler"))
        menu.addAction("Internal Data", lambda: self.goto_other_program("internal_data"))
        menu.addAction("Image Scraping", lambda: self.goto_other_program("image_scraping"))
        menu.addAction("Update Level Model", lambda: self.goto_other_program("external_update"))
        menu.addAction("AI Classification", lambda: self.goto_other_program("ai_classification"))
        menu.addAction("Basket Arrangement", lambda: self.goto_other_program("basket_arrangement"))
        self.context_button.setMenu(menu)
        top_bar.addWidget(self.context_button, alignment=Qt.AlignmentFlag.AlignRight)

        main_layout.addLayout(top_bar)
        main_layout.addSpacing(0)

        # Thêm RadioButton để chọn phương thức upload
        upload_method_group = QGroupBox("Phương thức Upload")
        upload_method_layout = QHBoxLayout(upload_method_group)  # Thay đổi từ QVBoxLayout sang QHBoxLayout
        self.rb_gsheet = QRadioButton("Upload lên Google Sheet")
        self.rb_bigquery = QRadioButton("Upload lên BigQuery")
        self.rb_gsheet.setChecked(True)  # Mặc định là Google Sheet
        upload_method_layout.addWidget(self.rb_gsheet)
        upload_method_layout.addWidget(self.rb_bigquery)
        main_layout.addWidget(upload_method_group)

        # Kết nối sự kiện khi thay đổi phương thức
        self.rb_gsheet.toggled.connect(self.toggle_upload_method)
        self.rb_bigquery.toggled.connect(self.toggle_upload_method)

        # Thêm GroupBox cho chế độ upload BigQuery
        self.bigquery_mode_group = QGroupBox("Chế độ Upload BigQuery")
        bigquery_mode_layout = QHBoxLayout(self.bigquery_mode_group)
        self.rb_append = QRadioButton("Append")
        self.rb_replace = QRadioButton("Replace")
        self.rb_append.setChecked(True)  # Mặc định là Append
        bigquery_mode_layout.addWidget(self.rb_append)
        bigquery_mode_layout.addWidget(self.rb_replace)
        main_layout.addWidget(self.bigquery_mode_group)
        self.bigquery_mode_group.setVisible(False)  # Ban đầu ẩn đi

        # GroupBox: Google Sheet Info
        sheet_groupbox = QGroupBox("Google Sheet Info")
        sheet_form = QFormLayout(sheet_groupbox)
        label_link = QLabel("Google Sheet ID:")
        self.import_sheet_id_edit = QLineEdit()
        self.import_sheet_id_edit.setPlaceholderText("Nhập Google Sheet ID hoặc dán URL của sheet...")
        self.import_sheet_id_edit.editingFinished.connect(self.normalize_sheet_id)
        sheet_form.addRow(label_link, self.import_sheet_id_edit)

        btn_load = QPushButton("Load danh sách Spreadsheet")
        btn_load.clicked.connect(self.load_import_spreadsheet)
        sheet_form.addRow(btn_load)

        # ComboBox để hiển thị danh sách worksheet có sẵn
        self.import_sheet_combo = QComboBox()
        self.import_sheet_combo.addItem("[Tạo sheet mới]")
        sheet_form.addRow(QLabel("Available Worksheets:"), self.import_sheet_combo)

        main_layout.addWidget(sheet_groupbox)

        # GroupBox: File Selection
        file_groupbox = QGroupBox("File Selection")
        file_layout = QFormLayout(file_groupbox)
        self.btn_browse = QPushButton("Chọn file Excel/CSV")
        self.btn_browse.clicked.connect(self.browse_import_file)
        file_layout.addRow(self.btn_browse)
        self.import_file_label = QLabel("Chưa có file được chọn")
        file_layout.addRow(QLabel("Selected File:"), self.import_file_label)
        main_layout.addWidget(file_groupbox)

        # GroupBox: Actions
        action_groupbox = QGroupBox("Quá trình xử lý")
        action_layout = QVBoxLayout(action_groupbox)
        self.btn_upload = QPushButton("Upload dữ liệu")
        self.btn_upload.clicked.connect(self.upload_import_data)
        action_layout.addWidget(self.btn_upload)
        self.import_progress_text = QTextEdit()
        self.import_progress_text.setReadOnly(True)
        action_layout.addWidget(self.import_progress_text)
        main_layout.addWidget(action_groupbox)


    def load_import_spreadsheet(self):
        sheet_id = self.import_sheet_id_edit.text().strip()
        if not sheet_id:
            QMessageBox.warning(self, "Warning 🚧", "Vui lòng nhập Google Sheet ID!")
            return
        try:
            sh = self.sheet_manager.client.open_by_key(sheet_id)
            self.import_spreadsheet = sh
            self.import_progress_text.append("Kết nối đến Spreadsheet thành công.")
            # Cập nhật danh sách worksheet vào ComboBox
            self.import_sheet_combo.clear()
            self.import_sheet_combo.addItem("[Tạo sheet mới]")
            for ws in sh.worksheets():
                self.import_sheet_combo.addItem(ws.title)
            self.import_progress_text.append("Đã tải danh sách worksheet.")
        except Exception as e:
            QMessageBox.critical(self, "Error ❌", f"Lỗi load Spreadsheet: {e}")
            self.import_progress_text.append(f"Lỗi: {e}")

    def browse_import_file(self):
        file_path, _ = QFileDialog.getOpenFileName(self, "Chọn file Excel hoặc CSV", "", "Excel/CSV Files (*.xlsx *.xls *.csv)")
        if file_path:
            self.current_import_file = file_path
            self.import_file_label.setText(os.path.basename(file_path))
            self.import_progress_text.append(f"Đã chọn file: {file_path}")

    def upload_import_data(self):
        if not self.current_import_file:
            QMessageBox.warning(self, "Warning 🚧", "Vui lòng chọn file Excel hoặc CSV!")
            return
        if not self.import_spreadsheet:
            QMessageBox.warning(self, "Warning 🚧", "Vui lòng load Spreadsheet!")
            return

        selected_sheet = self.import_sheet_combo.currentText()
        try:
            file_path = self.current_import_file
            self.import_progress_text.append(f"Đang đọc file: {file_path}")

            # Kiểm tra loại file và đọc tương ứng
            file_ext = os.path.splitext(file_path)[1].lower()
            if file_ext == '.csv':
                # Thử đọc CSV với các encoding khác nhau
                try:
                    df = pd.read_csv(file_path, encoding='utf-8')
                except UnicodeDecodeError:
                    try:
                        df = pd.read_csv(file_path, encoding='utf-8-sig')
                    except UnicodeDecodeError:
                        try:
                            df = pd.read_csv(file_path, encoding='cp1252')
                        except UnicodeDecodeError:
                            df = pd.read_csv(file_path, encoding='latin-1')
            else:
                df = pd.read_excel(file_path)

            self.import_progress_text.append(f"Đã đọc {df.shape[0]} hàng, {df.shape[1]} cột.")

            # Xử lý tên cột: đổi các cột chứa "Unnamed" thành chuỗi rỗng
            df.columns = ["" if "Unnamed" in str(col) else col for col in df.columns]
            # Ép cột thứ 3 (nếu có) sang kiểu chuỗi
            if len(df.columns) > 2:
                df.iloc[:, 2] = df.iloc[:, 2].astype(str)

            # Mở lại spreadsheet theo sheet ID đã load
            sheet_id = self.import_spreadsheet.id
            sh = self.sheet_manager.client.open_by_key(sheet_id)
            if selected_sheet == "[Tạo sheet mới]":
                new_sheet_name, ok = QInputDialog.getText(self, "Create Sheet", "Nhập tên sheet mới:")
                sheet_name = new_sheet_name.strip() if ok and new_sheet_name.strip() else "NewSheet"
                ws = sh.add_worksheet(title=sheet_name, rows="100", cols="20")
                append_mode = False  # Initialize append_mode for new sheet
            else:
                ws = sh.worksheet(selected_sheet)
                # Optimize: check column D for existing data, data will still be written starting from column A
                existing_data = ws.col_values(4)
                append_mode = False
                if existing_data and any(existing_data):
                    msg_box = QMessageBox(self)
                    msg_box.setWindowTitle("Tùy chọn nhập dữ liệu")
                    msg_box.setText("Sheet đã có dữ liệu. Bạn muốn nhập tiếp hay ghi đè?")
                    btn_append = msg_box.addButton("Nhập tiếp", QMessageBox.ButtonRole.YesRole)
                    btn_overwrite = msg_box.addButton("Ghi đè", QMessageBox.ButtonRole.NoRole)
                    # Increase font size and add spacing between buttons
                    btn_append.setStyleSheet("font-size: 16px; padding: 8px 16px; margin-right: 20px;")
                    btn_overwrite.setStyleSheet("font-size: 16px; padding: 8px 16px;")
                    msg_box.exec()
                    if msg_box.clickedButton() == btn_append:
                        append_mode = True
                    else:
                        ws.clear()

            # Xóa toàn bộ dữ liệu cũ trước khi upload mới
            if not append_mode:
                ws.clear()

            # Thực hiện upload theo từng chunk (batch)
            chunk_size = 20000  # 20k dòng mỗi batch
            total_rows = df.shape[0]
            num_batches = (total_rows // chunk_size) + (1 if total_rows % chunk_size else 0)
            self.import_progress_text.append(f"Đang tách dữ liệu thành {num_batches} phần...")
            QApplication.processEvents()

            for i in range(num_batches):
                if append_mode:
                    last_row = len(existing_data)
                    start_row = last_row + (i * chunk_size) + 1
                    include_header = False
                else:
                    start_row = i * chunk_size + 1
                    include_header = (i == 0)
                chunk_df = df.iloc[i * chunk_size: (i + 1) * chunk_size]
                self.import_progress_text.append(
                    f"Upload phần {i + 1}/{num_batches}: dòng {start_row} đến {start_row + len(chunk_df) - 1}"
                )
                QApplication.processEvents()
                # Sử dụng set_with_dataframe để ghi dữ liệu từ vị trí tương ứng
                set_with_dataframe(ws, chunk_df, row=start_row, col=1, include_index=False, include_column_header=include_header)

            # Sau khi upload xong, định dạng cột C cho toàn bộ dữ liệu (dành cho dữ liệu dạng TEXT)
            self.import_progress_text.append("Đang định dạng cột C thành Plain Text...")
            QApplication.processEvents()

            # Tính toán khoảng định dạng dựa vào số dòng thực tế đã import
            start_row = 1  # Bắt đầu từ dòng header
            end_row = total_rows + 1  # +1 cho header row

            # Nếu số dòng quá nhiều, có thể cần chia thành nhiều request
            max_cells_per_request = 50000
            format_chunk_size = max_cells_per_request
            num_format_chunks = (end_row // format_chunk_size) + (1 if end_row % format_chunk_size else 0)

            # Định dạng từng chunk cột C
            for i in range(num_format_chunks):
                chunk_start = start_row + (i * format_chunk_size)
                chunk_end = min(start_row + ((i + 1) * format_chunk_size) - 1, end_row)

                # Tạo range cho chunk hiện tại
                cell_range = f'C{chunk_start}:C{chunk_end}'

                # Áp dụng định dạng TEXT cho cell range
                format_json = {
                    "numberFormat": {
                        "type": "TEXT"
                    }
                }

                self.import_progress_text.append(f"Định dạng cột C từ dòng {chunk_start} đến dòng {chunk_end}...")
                QApplication.processEvents()

                # Gọi API để định dạng
                ws.format(cell_range, format_json)

            self.import_progress_text.append("Upload dữ liệu thành công!")
            QMessageBox.information(self, "Success", "Upload dữ liệu thành công!")

            # Sau khi upload thành công, xóa file tạm
            temp_file_id = os.path.join(tempfile.gettempdir(), "temp_spreadsheet_id.txt")
            if os.path.exists(temp_file_id):
                os.remove(temp_file_id)
        except Exception as e:
            QMessageBox.critical(self, "Error ❌", f"Lỗi xử lý file: {e}")
            self.import_progress_text.append(f"Lỗi: {e}")

    def extract_sheet_id(self, text: str) -> str:
        match = re.search(r'https://docs\.google\.com/spreadsheets/d/([^/]+)', text)
        if match:
            return match.group(1)
        return text

    def normalize_sheet_id(self):
        current = self.import_sheet_id_edit.text().strip()
        new_id = self.extract_sheet_id(current)
        if new_id != current:
            self.import_sheet_id_edit.setText(new_id)

    def handle_back(self):
        self.reset_ui()
        if hasattr(self, 'back_callback'):
            self.back_callback()

    def on_module_activated(self):
        """Được gọi khi module được kích hoạt từ main.py - reset hoàn toàn UI"""
        self.reset_ui()

    def handle_goto_internal(self):
        if hasattr(self, 'goto_internal_callback'):
            self.goto_internal_callback()

    def goto_other_program(self, program_name):
        # Keep current data, simply switch
        pass

    def reset_ui(self):
        self.import_sheet_id_edit.clear()
        self.import_sheet_combo.clear()
        self.import_sheet_combo.addItem("[Tạo sheet mới]")
        self.import_file_label.setText("Chưa có file được chọn")
        self.import_progress_text.clear()
        self.current_import_file = None
        self.import_spreadsheet = None

    def keyPressEvent(self, event):
        if event.key() == Qt.Key.Key_Backspace:
            self.handle_back()
        else:
            super().keyPressEvent(event)

    def stop_import(self):
        """Stop all import operations safely"""
        if hasattr(self, 'sheet_manager'):
            try:
                self.sheet_manager.client.session.close()
            except:
                pass

        # Clear any ongoing operations
        if hasattr(self, 'import_spreadsheet'):
            self.import_spreadsheet = None

        # Reset UI
        self.reset_ui()

    def closeEvent(self, event):
        """Cleanup temp files khi đóng"""
        import os, tempfile
        appdata_dir = os.getenv('LOCALAPPDATA')
        app_dir = os.path.join(appdata_dir, 'Data All in One')
        import_dir = os.path.join(app_dir, 'Import Data')

        if not os.path.exists(import_dir):
            os.makedirs(import_dir)

        temp_files = [
            "temp_spreadsheet_id.txt",
            "raw_processed_dest.txt"
        ]

        # Chỉ xóa các tệp tạm, giữ lại token BigQuery
        for fname in temp_files:
            fpath = os.path.join(import_dir, fname)
            if os.path.exists(fpath):
                try:
                    os.remove(fpath)
                except:
                    pass

        # Đóng các phiên làm việc
        self.stop_import()
        super().closeEvent(event)

    def check_temp_spreadsheet_file(self):
        """
        Kiểm tra và nạp dữ liệu từ temp files:
        - spreadsheet_id: ID của sheet đích
        - dest_data: ID và sheet name dùng cho import
        """
        import os
        appdata_dir = os.getenv('LOCALAPPDATA')
        app_dir = os.path.join(appdata_dir, 'Data All in One')
        import_dir = os.path.join(app_dir, 'Import Data')

        if not os.path.exists(import_dir):
            os.makedirs(import_dir)

        files_found = []
        data = {}

        # 1. Check raw_processed_dest.txt
        raw_dest_file = os.path.join(import_dir, "raw_processed_dest.txt")
        if os.path.exists(raw_dest_file):
            files_found.append("Raw processed destination data")
            with open(raw_dest_file, "r", encoding="utf-8") as f:
                lines = [line.strip() for line in f]
                if len(lines) >= 2:
                    data["raw_dest"] = {
                        "id": lines[0],
                        "sheet": lines[1]
                    }

        # 2. Check temp_spreadsheet_id.txt
        spreadsheet_file = os.path.join(import_dir, "temp_spreadsheet_id.txt")
        if os.path.exists(spreadsheet_file):
            files_found.append("Spreadsheet ID")
            with open(spreadsheet_file, "r", encoding="utf-8") as f:
                data["spreadsheet_id"] = f.read().strip()

        if files_found:
            reply = QMessageBox.question(
                self,
                "Import Data",
                f"Đã tìm thấy nguồn dữ liệu sẵn có: {', '.join(files_found)}.\nBạn có muốn sử dụng không?",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            if reply == QMessageBox.StandardButton.Yes:
                # Load data from temp files
                if "raw_dest" in data:
                    self.import_sheet_id_edit.setText(data["raw_dest"]["id"])
                    self.load_import_spreadsheet()
                    idx = self.import_sheet_combo.findText(data["raw_dest"]["sheet"])
                    if idx >= 0:
                        self.import_sheet_combo.setCurrentIndex(idx)

                elif "spreadsheet_id" in data:
                    self.import_sheet_id_edit.setText(data["spreadsheet_id"])
                    self.load_import_spreadsheet()

    def show_import_data(self):
        # ...existing code...
        self.check_temp_spreadsheet_file()  # <-- call only when user selects Import Data
        # ...existing code...

    def eventFilter(self, obj, event):
        if obj == self.context_button and event.type() == QEvent.Type.Enter:
            self.context_button.showMenu()
        return super().eventFilter(obj, event)

    def browse_multiple_files(self):
        """Cho phép chọn nhiều file Excel hoặc CSV để upload lên BigQuery"""
        file_paths, _ = QFileDialog.getOpenFileNames(
            self, "Chọn các file Excel hoặc CSV", "", "Excel/CSV Files (*.xlsx *.xls *.csv)"
        )
        if file_paths:
            self.current_import_files = file_paths
            self.import_file_label.setText(f"Đã chọn {len(file_paths)} file")
            self.import_progress_text.append(f"Đã chọn {len(file_paths)} file")

    def toggle_upload_method(self):
        """Cập nhật giao diện dựa trên phương thức upload được chọn"""
        is_gsheet = self.rb_gsheet.isChecked()

        # Hiển thị/ẩn các thành phần liên quan đến GSheet
        sheet_group_boxes = [w for w in self.findChildren(QGroupBox) if "Google Sheet Info" in w.title()]
        if sheet_group_boxes:
            sheet_groupbox = sheet_group_boxes[0]
            sheet_groupbox.setVisible(is_gsheet)

        # Hiển thị/ẩn tùy chọn chế độ BigQuery
        self.bigquery_mode_group.setVisible(not is_gsheet)

        # Đổi nút Browse cho phù hợp
        if is_gsheet:
            self.btn_browse.setText("Chọn file Excel/CSV")
            try:
                self.btn_browse.clicked.disconnect()
            except:
                pass
            self.btn_browse.clicked.connect(self.browse_import_file)
            self.btn_upload.setText("Upload dữ liệu lên Google Sheet")
            try:
                self.btn_upload.clicked.disconnect()
            except:
                pass
            self.btn_upload.clicked.connect(self.upload_import_data)
        else:
            self.btn_browse.setText("Chọn file Excel/CSV")
            try:
                self.btn_browse.clicked.disconnect()
            except:
                pass
            self.btn_browse.clicked.connect(self.browse_multiple_files)
            self.btn_upload.setText("Upload dữ liệu lên BigQuery")
            try:
                self.btn_upload.clicked.disconnect()
            except:
                pass
            self.btn_upload.clicked.connect(self.upload_to_bigquery)

    def get_bigquery_clients(self):
        """Khởi tạo BigQuery và Storage clients"""
        try:
            # Tạo file credentials tạm từ base64
            temp_dir = tempfile.gettempdir()
            credentials_path = os.path.join(temp_dir, 'bigquery_credentials.json')
            credentials_json = json.loads(base64.b64decode(BQ_CREDENTIALS))
            with open(credentials_path, 'w') as f:
                json.dump(credentials_json, f)

            # Tạo đường dẫn riêng biệt cho token BigQuery
            appdata_dir = os.getenv('LOCALAPPDATA')
            app_dir = os.path.join(appdata_dir, 'Data All in One')
            token_dir = os.path.join(app_dir, 'BigQuery')

            if not os.path.exists(token_dir):
                os.makedirs(token_dir)

            token_path = os.path.join(token_dir, 'bigquery_token.json')

            # Kiểm tra xem token có tồn tại không
            creds = None
            if os.path.exists(token_path):
                try:
                    with open(token_path, 'r') as token_file:
                        creds_data = json.load(token_file)
                        creds = Credentials.from_authorized_user_info(creds_data, BQ_SCOPES)

                    # Làm mới token nếu hết hạn
                    if creds.expired and creds.refresh_token:
                        creds.refresh(Request())
                        with open(token_path, 'w') as token_file:
                            token_file.write(creds.to_json())
                except Exception:
                    # Không hiển thị lỗi chi tiết
                    creds = None

            if not creds:
                # Tạo token mới - đơn giản hóa thông báo
                flow = InstalledAppFlow.from_client_secrets_file(
                    credentials_path,
                    BQ_SCOPES,
                    redirect_uri='http://localhost'
                )
                creds = flow.run_local_server(port=0)
                with open(token_path, 'w') as token_file:
                    token_file.write(creds.to_json())

            # Khởi tạo clients
            storage_client = storage.Client(project=BQ_PROJECT_ID, credentials=creds)
            bigquery_client = bigquery.Client(project=BQ_PROJECT_ID, credentials=creds)

            # Xóa file credentials tạm
            if os.path.exists(credentials_path):
                os.remove(credentials_path)

            return storage_client, bigquery_client
        except Exception as e:
            self.import_progress_text.append(f"Lỗi kết nối: {e}")
            raise e

    def convert_excel_to_csv(self, excel_file_path):
        """Chuyển đổi file Excel sang CSV"""
        try:
            df = pd.read_excel(excel_file_path)
            # Tạo tên file CSV từ tên file Excel
            filename = os.path.basename(excel_file_path)
            filename_without_ext = os.path.splitext(filename)[0]

            # Tạo tên file với định dạng YYYY_MM_DD
            today = datetime.now().strftime("%Y_%m_%d")
            csv_filename = f"{filename_without_ext}_{today}.csv"

            # Lưu vào thư mục tạm
            temp_dir = tempfile.gettempdir()
            csv_path = os.path.join(temp_dir, csv_filename)

            # Xử lý dữ liệu trước khi lưu
            # Thay thế giá trị None và NaN bằng chuỗi rỗng
            df.fillna('', inplace=True)

            # Chuyển tất cả cột về kiểu chuỗi để ngăn lỗi khi ghi
            for col in df.columns:
                df[col] = df[col].astype(str)

                # Cắt bớt chuỗi quá dài để tránh lỗi field_size_limit
                max_length = 100000  # Giới hạn an toàn cho mỗi trường
                df[col] = df[col].apply(lambda x: x[:max_length] if len(x) > max_length else x)

            # Lưu với encoding utf-8
            df.to_csv(csv_path, index=False, encoding='utf-8', quoting=csv.QUOTE_ALL)

            # Chỉ giữ lại thông báo quan trọng
            self.import_progress_text.append(f"Đang xử lý file: {filename}")
            return csv_path
        except Exception as e:
            self.import_progress_text.append(f"Lỗi xử lý file {os.path.basename(excel_file_path)}: {e}")
            raise e

    def upload_to_bucket(self, file_path, storage_client):
        """Upload file lên Google Cloud Storage Bucket"""
        try:
            bucket = storage_client.bucket(BQ_BUCKET_NAME)

            # Tên blob là tên file
            blob_name = os.path.basename(file_path)
            blob = bucket.blob(blob_name)

            # Upload file
            blob.upload_from_filename(file_path)

            # Loại bỏ thông báo chi tiết
            return blob_name
        except Exception as e:
            self.import_progress_text.append(f"Lỗi upload dữ liệu: {e}")
            raise e

    def parse_header(self, header_row):
        """Phân tích cấu trúc header của file CSV"""
        mapping = {}
        encountered_chuyen_muc = 0

        if all(col.strip() in EXPECTED_HEADER for col in header_row if col.strip()):
            # Đã khớp với các cột mong đợi
            for idx, col in enumerate(header_row):
                col = col.strip()
                if col in EXPECTED_HEADER:
                    mapping[col] = idx
            return mapping

        # Nếu không, giả định tiếng Việt hoặc một phần
        for idx, col in enumerate(header_row):
            col = col.strip()
            if col in VIETNAMESE_MAPPING:
                new_col = VIETNAMESE_MAPPING[col]
                if col == "Chuyên mục":
                    if encountered_chuyen_muc == 0:
                        new_col = "cate_code1"
                    elif encountered_chuyen_muc == 1:
                        new_col = "cate_code2"
                    else:
                        continue  # bỏ qua thứ 3
                    encountered_chuyen_muc += 1
                if new_col is not None:
                    mapping[new_col] = idx
            else:
                # Nếu khớp với cột tiếng Anh từ EXPECTED_HEADER, ánh xạ nó
                if col in EXPECTED_HEADER:
                    mapping[col] = idx

        return mapping

    def parse_as_int(self, value):
        """Phân tích an toàn giá trị thành số nguyên. Trả về '' nếu phân tích thất bại."""
        try:
            v = value.replace(',', '').strip()
            return str(int(float(v)))  # xử lý "123.0" hoặc "123"
        except:
            return ""

    def parse_as_float(self, value):
        """Phân tích an toàn giá trị thành số thực. Trả về '' nếu phân tích thất bại."""
        try:
            v = value.replace(',', '').strip()
            return str(float(v))
        except:
            return ""

    def parse_as_date(self, value):
        """Phân tích an toàn ngày tháng. Trả về '' nếu phân tích thất bại."""
        try:
            dt = parse(value.strip())
            return dt.strftime("%Y-%m-%d")
        except:
            return ""

    def clean_csv_data(self, bucket_name, file_name, storage_client, existing_item_ids=None, table_name=None):
        """Làm sạch dữ liệu CSV trước khi upload vào BigQuery"""
        bucket = storage_client.bucket(bucket_name)
        blob = bucket.blob(file_name)
        if not blob.exists():
            self.import_progress_text.append(f"Bỏ qua file không tồn tại: {file_name}")
            return None

        # Giảm bớt thông báo chi tiết
        try:
            csv_data = blob.download_as_text()
            input_stream = io.StringIO(csv_data)

            # Đọc CSV với xử lý ngoại lệ
            try:
                reader = csv.reader(input_stream)
                all_rows = list(reader)
            except csv.Error as e:
                self.import_progress_text.append(f"Lỗi đọc CSV: {e}. Thử phương pháp thay thế...")

                # Thử phương pháp thay thế: đọc theo dòng và phân tích
                input_stream.seek(0)  # Reset stream
                all_rows = []
                for line in input_stream:
                    try:
                        row = next(csv.reader([line]))
                        all_rows.append(row)
                    except csv.Error:
                        # Nếu dòng có lỗi, thử tách thủ công
                        cells = line.split(',')
                        # Loại bỏ dấu ngoặc kép và khoảng trắng
                        cells = [cell.strip().strip('"') for cell in cells]
                        all_rows.append(cells)

            total_lines_in_csv = len(all_rows)
            if total_lines_in_csv == 0:
                self.import_progress_text.append(f"File `{file_name}` trống. Bỏ qua.")
                return None

            header_row = all_rows[0]
            data_rows = all_rows[1:]
            header_mapping = self.parse_header(header_row)

            # Thống kê
            header_lines = 1
            data_lines = len(data_rows)
            skipped_count = 0
            duplicate_count = 0
            used_count = 0
            replaced_count = 0

            output_stream = io.StringIO()
            writer = csv.writer(output_stream, quoting=csv.QUOTE_ALL)
            writer.writerow(EXPECTED_HEADER)

            # Tìm CSV index cho "item_id" nếu nó tồn tại
            item_id_index = header_mapping.get("item_id", -1)

            # Tạo danh sách item_id cần thay thế nếu ở chế độ replace
            replaced_item_ids = set()
            replace_mode = hasattr(self, 'rb_replace') and self.rb_replace.isChecked()

            for row in data_rows:
                if not any(cell.strip() for cell in row):
                    # Dòng hoàn toàn trống
                    skipped_count += 1
                    continue

                # Kiểm tra điều kiện sub-header: nếu cột "item_id" có "Mã Shop", bỏ qua
                if item_id_index >= 0 and item_id_index < len(row):
                    raw_item_id = row[item_id_index].strip().lower()
                    if "mã shop" in raw_item_id:
                        # Dòng sub-header => bỏ qua
                        skipped_count += 1
                        continue

                    # Kiểm tra item_id trùng lặp
                    if existing_item_ids and raw_item_id and raw_item_id in existing_item_ids:
                        if replace_mode:
                            # Chế độ replace: thêm ID vào danh sách cần thay thế
                            replaced_item_ids.add(raw_item_id)
                            replaced_count += 1
                        else:
                            # Chế độ append: bỏ qua ID trùng
                            duplicate_count += 1
                            continue

                # Dòng bình thường => phân tích
                new_row = []
                for col_name in EXPECTED_HEADER:
                    idx = header_mapping.get(col_name, -1)
                    if idx >= 0 and idx < len(row):
                        raw_val = row[idx].strip()
                        # Giới hạn độ dài trường để tránh lỗi field size limit
                        if len(raw_val) > 100000:
                            raw_val = raw_val[:100000]
                    else:
                        raw_val = ""

                    if col_name in INT_COLUMNS:
                        parsed_val = self.parse_as_int(raw_val)
                        new_row.append(parsed_val)
                    elif col_name in FLOAT_COLUMNS:
                        parsed_val = self.parse_as_float(raw_val)
                        new_row.append(parsed_val)
                    elif col_name in DATE_COLUMNS:
                        parsed_val = self.parse_as_date(raw_val)
                        new_row.append(parsed_val)
                    else:
                        new_row.append(raw_val)

                writer.writerow(new_row)
                used_count += 1

            if used_count == 0:
                self.import_progress_text.append(f"File `{file_name}` không có dữ liệu hợp lệ. Bỏ qua.")
                return None

            cleaned_file_path = f"cleaned/{file_name}"
            new_blob = bucket.blob(cleaned_file_path)
            new_blob.upload_from_string(output_stream.getvalue(), content_type="text/csv")

            self.import_progress_text.append("==== BÁO CÁO LÀM SẠCH CSV ====")
            self.import_progress_text.append(f" - Tổng số dòng trong CSV: {total_lines_in_csv}")
            self.import_progress_text.append(f" - Dòng tiêu đề: {header_lines}")
            self.import_progress_text.append(f" - Dòng dữ liệu (sau tiêu đề): {data_lines}")
            self.import_progress_text.append(f" - Dòng bỏ qua: {skipped_count} (trống hoặc tiêu đề phụ)")
            if existing_item_ids is not None:
                if replace_mode:
                    self.import_progress_text.append(f" - Dòng sẽ thay thế do trùng item_id: {replaced_count}")
                else:
                    self.import_progress_text.append(f" - Dòng bỏ qua do trùng item_id: {duplicate_count}")
            self.import_progress_text.append(f" - Đã ghi vào CSV làm sạch: {used_count}")
            self.import_progress_text.append("================================")

            # Trả về thông tin bổ sung cho replace mode
            result_data = {
                "uri": f"gs://{bucket_name}/{cleaned_file_path}",
                "replaced_item_ids": list(replaced_item_ids) if replace_mode else []
            }
            return result_data
        except Exception as e:
            self.import_progress_text.append(f"Lỗi làm sạch CSV: {e}")
            return None

    def upload_csv_to_bigquery(self, bucket_name, dataset_id, file_name, storage_client, bigquery_client):
        """
        Quy trình upload lên BigQuery:
        1) Làm sạch CSV (bỏ qua dòng sub-header nơi mà item_id có "mã shop").
        2) Nếu ở chế độ replace, xóa các bản ghi có item_id trùng lặp.
        3) Tải lên bảng BQ đặt tên theo mẫu ngày (YYYY_MM_DD).
        4) Xóa bản gốc + đã làm sạch nếu thành công.
        """
        match = re.search(r'(\d{4}_\d{2}_\d{2})', file_name)
        if not match:
            # Nếu tên file không có định dạng ngày, dùng ngày hiện tại
            today = datetime.now().strftime("%Y_%m_%d")
            table_name = today
        else:
            table_name = match.group(1)

        table_id = f"{dataset_id}.{table_name}"

        # Kiểm tra xem bảng đã tồn tại chưa và lấy danh sách item_id hiện có
        existing_item_ids = set()
        table_exists = False
        try:
            # Kiểm tra nếu bảng đã tồn tại
            table = bigquery_client.get_table(table_id)
            table_exists = True

            # Xác định kiểu dữ liệu của cột item_id
            item_id_column_type = None
            for field in table.schema:
                if field.name == 'item_id':
                    item_id_column_type = field.field_type
                    break

            # Truy vấn tất cả item_id hiện có trong bảng
            query = f"SELECT DISTINCT item_id FROM `{table_id}` WHERE item_id IS NOT NULL"
            query_job = bigquery_client.query(query)
            results = query_job.result()

            # Thêm tất cả item_id vào set để tìm kiếm nhanh
            for row in results:
                if row.item_id:
                    existing_item_ids.add(str(row.item_id).strip())

            self.import_progress_text.append(f"Đã tìm thấy {len(existing_item_ids)} item_id hiện có trong bảng {table_name}")
        except Exception as e:
            # Bảng chưa tồn tại hoặc có lỗi truy vấn
            self.import_progress_text.append(f"Bảng {table_name} chưa tồn tại hoặc không thể truy vấn: {e}")

        # Làm sạch CSV và xử lý item_id dựa trên chế độ upload (append hoặc replace)
        cleaned_data = self.clean_csv_data(bucket_name, file_name, storage_client, existing_item_ids, table_name)
        if not cleaned_data:
            return None

        # Kiểm tra chế độ replace
        replace_mode = hasattr(self, 'rb_replace') and self.rb_replace.isChecked()
        if replace_mode and table_exists and cleaned_data.get("replaced_item_ids"):
            # Thực hiện xóa các bản ghi có item_id trong danh sách cần thay thế
            replaced_ids = cleaned_data["replaced_item_ids"]
            if replaced_ids:
                try:
                    # Chuyển đổi item_id thành số nguyên nếu có thể
                    numeric_ids = []
                    for id_str in replaced_ids:
                        try:
                            # Thử chuyển đổi thành số nguyên
                            numeric_ids.append(int(id_str))
                        except (ValueError, TypeError):
                            # Nếu không thể chuyển thành số, giữ nguyên dạng chuỗi
                            pass

                    if numeric_ids:
                        # Giới hạn số lượng ID trong một truy vấn (để tránh vượt quá giới hạn)
                        ids_per_query = 1000
                        num_batches = (len(numeric_ids) // ids_per_query) + (1 if len(numeric_ids) % ids_per_query else 0)

                        for i in range(num_batches):
                            batch_ids = numeric_ids[i * ids_per_query : (i + 1) * ids_per_query]
                            # Không thêm dấu nháy đơn vào ID số
                            ids_str = ", ".join([str(id) for id in batch_ids])
                            delete_query = f"DELETE FROM `{table_id}` WHERE item_id IN ({ids_str})"

                            self.import_progress_text.append(f"Đang xóa {len(batch_ids)} bản ghi trùng lặp (batch {i+1}/{num_batches})...")
                            delete_job = bigquery_client.query(delete_query)
                            delete_job.result()  # Chờ hoàn thành

                        self.import_progress_text.append(f"Đã xóa {len(numeric_ids)} bản ghi trùng lặp để chuẩn bị thay thế")
                    else:
                        self.import_progress_text.append("Không có ID số nào để xóa")
                except Exception as e:
                    self.import_progress_text.append(f"Lỗi khi xóa bản ghi trùng lặp: {e}")

        # Lấy URI cho CSV đã làm sạch
        cleaned_gcs_uri = cleaned_data["uri"] if isinstance(cleaned_data, dict) else cleaned_data

        # Định nghĩa schema cho BigQuery
        schema = [
            bigquery.SchemaField(name, "STRING" if name not in INT_COLUMNS and
                                              name not in FLOAT_COLUMNS and
                                              name not in DATE_COLUMNS else
                                 "INT64" if name in INT_COLUMNS else
                                 "FLOAT64" if name in FLOAT_COLUMNS else
                                 "DATE")
            for name in EXPECTED_HEADER
        ]

        job_config = bigquery.LoadJobConfig(
            source_format=bigquery.SourceFormat.CSV,
            skip_leading_rows=1,  # Đã bỏ qua dòng tiêu đề
            allow_quoted_newlines=True,
            max_bad_records=100,
            autodetect=False,
            schema=schema,
            write_disposition="WRITE_APPEND",  # Luôn dùng WRITE_APPEND
        )

        # Đơn giản hóa thông báo
        self.import_progress_text.append(f"Đang tải dữ liệu vào BigQuery...")
        try:
            load_job = bigquery_client.load_table_from_uri(cleaned_gcs_uri, table_id, job_config=job_config)
            load_job.result()  # chờ đến khi hoàn thành
            loaded_rows = load_job.output_rows
            self.import_progress_text.append(f"Đã tải {loaded_rows} dòng vào bảng {table_name}")

            # Dọn dẹp nếu thành công - không hiển thị thông báo chi tiết
            bucket = storage_client.bucket(bucket_name)
            bucket.blob(file_name).delete()
            bucket.blob(f"cleaned/{file_name}").delete()

            return table_name
        except Exception as e:
            self.import_progress_text.append(f"Lỗi tải lên BigQuery: {e}")
            return None

    def upload_to_bigquery(self):
        """Thực hiện upload lên BigQuery thông qua Bucket"""
        if not self.current_import_files:
            QMessageBox.warning(self, "Cảnh báo 🚧", "Vui lòng chọn file Excel hoặc CSV!")
            return

        try:
            self.import_progress_text.clear()  # Xóa thông báo cũ
            self.import_progress_text.append("Đang upload dữ liệu lên BigQuery...")

            # Hiển thị thông báo cho người dùng về quá trình xác thực
            msg = QMessageBox()
            msg.setIcon(QMessageBox.Icon.Information)
            msg.setWindowTitle("Xác thực BigQuery")
            msg.setText("Sắp mở trình duyệt để xác thực với BigQuery")
            msg.setInformativeText("Hãy đảm bảo đăng nhập vào tài khoản có quyền truy cập dự án BigQuery.")
            msg.setStandardButtons(QMessageBox.StandardButton.Ok)
            msg.exec()

            # Khởi tạo BigQuery và Storage clients
            storage_client, bigquery_client = self.get_bigquery_clients()

            uploaded_files = []
            temp_files = []

            # Hiển thị số lượng file đang xử lý
            self.import_progress_text.append(f"Đang xử lý {len(self.current_import_files)} file...")

            # Xử lý từng file được chọn
            for file_path in self.current_import_files:
                file_ext = os.path.splitext(file_path)[1].lower()

                if file_ext in ['.xlsx', '.xls']:
                    # Nếu là Excel, convert sang CSV trước
                    self.import_progress_text.append(f"Đang chuyển đổi Excel sang CSV: {os.path.basename(file_path)}")
                    csv_path = self.convert_excel_to_csv(file_path)
                    temp_files.append(csv_path)  # Thêm vào danh sách file tạm để xóa sau
                elif file_ext == '.csv':
                    # Nếu là CSV, sử dụng trực tiếp
                    self.import_progress_text.append(f"Đã phát hiện file CSV: {os.path.basename(file_path)}")
                    csv_path = file_path
                else:
                    # Loại file không được hỗ trợ
                    self.import_progress_text.append(f"Bỏ qua file không hỗ trợ: {os.path.basename(file_path)}")
                    continue

                # Upload CSV lên bucket
                blob_name = self.upload_to_bucket(csv_path, storage_client)
                uploaded_files.append(blob_name)

            # Xử lý từng file CSV trên bucket và đưa vào BigQuery
            processed_tables = []
            for file_name in uploaded_files:
                table_name = self.upload_csv_to_bigquery(BQ_BUCKET_NAME, BQ_DATASET_ID, file_name, storage_client, bigquery_client)
                if table_name:
                    processed_tables.append(table_name)

            # Xóa file CSV tạm
            for csv_path in temp_files:
                if os.path.exists(csv_path):
                    os.remove(csv_path)

            # Thông báo kết quả cuối cùng
            if processed_tables:
                self.import_progress_text.append(f"Đã hoàn thành upload vào {len(processed_tables)} bảng.")
                QMessageBox.information(self, "Thành công", f"Đã upload dữ liệu lên BigQuery thành công vào {len(processed_tables)} bảng!")
            else:
                self.import_progress_text.append("Không có dữ liệu nào được xử lý thành công.")
                QMessageBox.warning(self, "Cảnh báo", "Không có dữ liệu nào được xử lý thành công!")

        except Exception as e:
            QMessageBox.critical(self, "Lỗi ❌", f"Lỗi trong quá trình xử lý: {e}")
            self.import_progress_text.append(f"Lỗi: {e}")
